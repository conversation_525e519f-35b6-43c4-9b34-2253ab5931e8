#!/usr/bin/env python3
"""
Test script for Excel export functionality.
Tests the new Excel export feature with template format matching.
"""

import sys
import os
from datetime import date, datetime
from pathlib import Path

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_export_service():
    """Test the Excel export service directly."""
    print("=== Testing Excel Export Service ===")

    try:
        # Import Django and setup
        import django

        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")
        django.setup()

        from orders.models import Order
        from orders.excel_export_service import (
            export_orders_to_excel,
            generate_representative_name,
        )
        from accounts.models import User

        # Get some test orders
        orders = Order.objects.all()[:5]  # Get first 5 orders

        if not orders.exists():
            print("❌ No orders found in database for testing")
            return False

        print(f"📊 Found {orders.count()} orders for testing")

        # Test representative name generation
        try:
            user = User.objects.first()
            if user:
                rep_name = generate_representative_name(user)
                print(f"✅ Representative name generated: '{rep_name}'")
            else:
                rep_name = "Test Office - Test User"
                print(f"⚠️  No users found, using default: '{rep_name}'")
        except Exception as e:
            rep_name = "Test Office - Test User"
            print(f"⚠️  Error generating rep name, using default: {e}")

        # Test export
        print(f"📁 Exporting {orders.count()} orders...")

        file_path = export_orders_to_excel(
            orders=orders,
            representative_name=rep_name,
            export_date=date.today(),
            filename="test_export.xlsx",
        )

        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✅ Export successful!")
            print(f"   File: {file_path}")
            print(f"   Size: {file_size:,} bytes")

            # Verify file structure
            try:
                import openpyxl

                wb = openpyxl.load_workbook(file_path)
                ws = wb.active

                print(f"   Sheet: {ws.title}")
                print(f"   Dimensions: {ws.max_row} rows x {ws.max_column} columns")

                # Check key cells
                rep_cell = ws["B3"].value
                date_cell = ws["B5"].value

                print(f"   Representative (B3): '{rep_cell}'")
                print(f"   Date (B5): '{date_cell}'")

                # Check headers
                print("   Headers (Row 6):")
                for col in range(1, 9):
                    cell = ws.cell(row=6, column=col)
                    print(f"     Column {col}: '{cell.value}'")

                # Check first data row
                if ws.max_row > 6:
                    print("   First data row (Row 7):")
                    for col in range(1, 9):
                        cell = ws.cell(row=7, column=col)
                        print(f"     Column {col}: '{cell.value}'")

                wb.close()

            except Exception as e:
                print(f"⚠️  Could not verify file structure: {e}")

            return True
        else:
            print(f"❌ Export failed - file not created")
            return False

    except Exception as e:
        print(f"❌ Error during export test: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_template_analysis():
    """Test template file analysis."""
    print("\n=== Testing Template Analysis ===")

    try:
        import openpyxl

        template_path = "نسخة مناديب اورجينال - Copy.xlsx"

        if not os.path.exists(template_path):
            print(f"❌ Template file not found: {template_path}")
            return False

        print(f"📁 Analyzing template: {template_path}")

        wb = openpyxl.load_workbook(template_path)
        ws = wb.active

        print(f"✅ Template loaded successfully")
        print(f"   Sheet: {ws.title}")
        print(f"   Dimensions: {ws.max_row} rows x {ws.max_column} columns")

        # Analyze structure
        print("\n📋 Template Structure:")
        print(f"   B3 (Rep label): '{ws['B3'].value}'")
        print(f"   B4 (Date label): '{ws['B4'].value}'")
        print(f"   B5 (Date value): '{ws['B5'].value}'")

        print("\n📋 Headers (Row 6):")
        headers = []
        for col in range(1, 9):
            cell = ws.cell(row=6, column=col)
            headers.append(cell.value)
            print(f"   Column {col}: '{cell.value}'")

        # Check for sample data
        print("\n📋 Sample Data:")
        data_rows = 0
        for row in range(7, min(12, ws.max_row + 1)):
            row_data = []
            has_data = False
            for col in range(1, 9):
                cell = ws.cell(row=row, column=col)
                row_data.append(cell.value)
                if cell.value is not None:
                    has_data = True

            if has_data:
                data_rows += 1
                print(f"   Row {row}: {row_data}")

        print(f"\n✅ Found {data_rows} sample data rows")

        wb.close()
        return True

    except Exception as e:
        print(f"❌ Error analyzing template: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_export_schemas():
    """Test export schemas and validation."""
    print("\n=== Testing Export Schemas ===")

    try:
        from orders.excel_export_schemas import (
            SelectiveExportRequest,
            BulkExportRequest,
            OrderExportFilters,
            DateRangeFilter,
            validate_export_request,
        )
        from datetime import date, timedelta

        # Test selective export request
        print("📋 Testing SelectiveExportRequest...")

        selective_data = {
            "export_type": "selective",
            "order_ids": [1, 2, 3, 4, 5],
            "filename": "test_selective_export.xlsx",
            "export_date": date.today(),
        }

        try:
            selective_request = SelectiveExportRequest(**selective_data)
            print(
                f"✅ Selective request valid: {len(selective_request.order_ids)} orders"
            )
        except Exception as e:
            print(f"❌ Selective request validation failed: {e}")
            return False

        # Test bulk export request
        print("📋 Testing BulkExportRequest...")

        # Create date range filter
        date_range = DateRangeFilter(
            start_date=date.today() - timedelta(days=30), end_date=date.today()
        )

        # Create filters
        filters = OrderExportFilters(
            created_date_range=date_range,
            status=["pending", "completed"],
            min_total_price=100.0,
            max_total_price=1000.0,
        )

        bulk_data = {
            "export_type": "bulk",
            "filters": filters,
            "filename": "test_bulk_export.xlsx",
            "limit": 1000,
        }

        try:
            bulk_request = BulkExportRequest(**bulk_data)
            print(
                f"✅ Bulk request valid with {len(bulk_request.filters.status or [])} status filters"
            )
        except Exception as e:
            print(f"❌ Bulk request validation failed: {e}")
            return False

        # Test validation helper
        print("📋 Testing validation helper...")

        try:
            validated = validate_export_request(selective_data)
            print(f"✅ Validation helper works: {type(validated).__name__}")
        except Exception as e:
            print(f"❌ Validation helper failed: {e}")
            return False

        return True

    except Exception as e:
        print(f"❌ Error testing schemas: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_column_mapping():
    """Test column mapping against template."""
    print("\n=== Testing Column Mapping ===")

    try:
        # Import Django and setup
        import django

        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")
        django.setup()

        from orders.models import Order

        # Get a sample order
        order = Order.objects.first()

        if not order:
            print("❌ No orders found for mapping test")
            return False

        print(f"📊 Testing mapping with order: {order.code}")

        # Test field mappings
        mappings = {
            "Serial Number (م)": 1,  # Auto-generated
            "Customer Name (الاسم)": order.customer_name,
            "Phone Number (التليفون)": order.customer_phone,
            "Company (الشركه)": order.customer_company.code
            if order.customer_company
            else "",
            "Price (السعر)": float(order.total_price) if order.total_price else 0,
            "Status (الحاله)": str(order.order_handling_status)
            if hasattr(order, "order_handling_status")
            else "",
            "Initial Status (حالة أولي)": float(order.total_price)
            if order.total_price
            else 0,
        }

        print("📋 Field Mappings:")
        for field, value in mappings.items():
            print(f"   {field}: '{value}'")

        # Test representative name generation
        from accounts.models import User

        user = User.objects.first()

        if user:
            from orders.excel_export_service import generate_representative_name

            rep_name = generate_representative_name(user)
            print(f"\n📋 Representative Name: '{rep_name}'")

        return True

    except Exception as e:
        print(f"❌ Error testing column mapping: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """Run all export tests."""
    print("🚀 Starting Excel Export Tests\n")

    tests = [
        ("Template Analysis", test_template_analysis),
        ("Export Schemas", test_export_schemas),
        ("Column Mapping", test_column_mapping),
        ("Export Service", test_export_service),
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n{'=' * 60}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'=' * 60}")

    # Summary
    print(f"\n🏁 Test Results Summary:")
    print(f"{'=' * 60}")

    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\nOverall: {passed}/{len(tests)} tests passed")

    if passed == len(tests):
        print("🎉 All tests passed! The Excel export feature is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
