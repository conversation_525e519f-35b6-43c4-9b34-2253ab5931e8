#!/usr/bin/env python3
"""
<PERSON>ript to analyze the specific sample Excel file and understand its structure.
"""

import pandas as pd
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_sample_excel():
    """Analyze the sample Excel file with proper handling of Arabic content."""
    
    file_path = "8-2-2025 رين واى.xlsx تمانيات.xlsx"
    
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return
    
    try:
        # Read the Excel file with different approaches to understand structure
        print("=== Reading Excel file with different methods ===\n")
        
        # Method 1: Read without header to see raw structure
        print("1. Raw data (first 10 rows):")
        df_raw = pd.read_excel(file_path, header=None)
        print(df_raw.head(10))
        print(f"Shape: {df_raw.shape}")
        print()
        
        # Method 2: Try reading with header at row 3 (where we detected headers)
        print("2. Data with header at row 3:")
        df_header = pd.read_excel(file_path, header=3)
        print("Columns:", list(df_header.columns))
        print(f"Shape: {df_header.shape}")
        print()
        
        # Show first few actual data rows
        print("3. First 5 data rows:")
        print(df_header.head())
        print()
        
        # Analyze data types and content
        print("4. Column analysis:")
        for i, col in enumerate(df_header.columns):
            col_data = df_header[col].dropna()
            print(f"Column {i}: '{col}'")
            print(f"  - Data type: {df_header[col].dtype}")
            print(f"  - Non-null count: {len(col_data)}")
            print(f"  - Sample values: {col_data.head(3).tolist()}")
            print()
            
        # Try to identify the actual column structure
        print("5. Suggested column mapping:")
        column_mapping = {
            0: "serial_number",  # First column appears to be serial/index
            1: "representative_name",  # اسم المندوب
            2: "customer_name",  # الاسم  
            3: "customer_phone",  # التليفون
            4: "code",  # الرمز
            5: "price",  # السعر
            6: "address",  # العنوان
            7: "company",  # الشركة
            8: "net_amount",  # صافي المبلغ بعد العمولة
            9: "status"  # الحالة
        }
        
        for i, (col_index, suggested_name) in enumerate(column_mapping.items()):
            if col_index < len(df_header.columns):
                actual_col = df_header.columns[col_index]
                print(f"  Column {col_index}: '{actual_col}' → {suggested_name}")
        
        # Show sample of clean data
        print("\n6. Sample of actual order data (rows 5-10):")
        sample_data = df_header.iloc[5:10]
        for idx, row in sample_data.iterrows():
            print(f"Row {idx}:")
            for i, value in enumerate(row):
                if i < len(column_mapping):
                    field_name = column_mapping[i]
                    print(f"  {field_name}: {value}")
            print()
            
    except Exception as e:
        print(f"Error analyzing file: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_sample_excel()
