import json
import pytest
from django.test import Client
from accounts.models import User, Role
from offices.models import Office


@pytest.fixture
def office():
    """Create a test office"""
    return Office.objects.create(
        name="Test Office", address="123 Test St", phone="************"
    )


@pytest.fixture
def master_user(office):
    """Create a master user for testing"""
    user = User.objects.create_user(
        username="master",
        password="password123",
        role=Role.MASTER,
        office=office,
        email="<EMAIL>",
    )
    return user


@pytest.fixture
def auth_client(client: Client, master_user):
    """Returns an authenticated client with master user token"""
    # Login to get token
    response = client.post(
        "/api/accounts/",
        query_params={
            "username": "master",
            "password": "password123",
            "office_id": master_user.office.id,
        },
        content_type="application/json",
    )
    token = json.loads(response.content)["token"]

    # Create a new client with authentication headers
    auth_client = Client(headers={"Authorization": f"Bearer {token}"})
    auth_client.defaults["Authorization"] = f"Bearer {token}"
    return auth_client


@pytest.mark.django_db
class TestMasterUserEmployeeEdgeCases:
    """Test edge cases for master user managing employees"""

    def test_create_employee_duplicate_username(self, auth_client):
        """Test creating an employee with a duplicate username"""
        # First create an employee
        employee_data = {
            "username": "unique_employee",
            "password": "password123",
            "first_name": "John",
            "last_name": "Doe",
            "role": Role.EMPLOYEE,
            "commission_rate": 10.0,
            "email": "<EMAIL>",
            "phone": "**********",
        }

        # Create first employee
        auth_client.post(
            "/api/accounts/create",
            data=json.dumps(employee_data),
            content_type="application/json",
        )

        # Try to create another employee with the same username
        response = auth_client.post(
            "/api/accounts/create",
            data=json.dumps(employee_data),
            content_type="application/json",
        )

        # Assert response (should fail with 400 or similar)
        assert response.status_code >= 400

    def test_edit_nonexistent_employee(self, auth_client):
        """Test editing a non-existent employee"""
        # Use a non-existent ID
        non_existent_id = 99999

        # Prepare updated data
        updated_data = {"first_name": "Updated", "last_name": "Name"}

        # Send request to edit non-existent employee
        response = auth_client.put(
            f"/api/accounts/edit/{non_existent_id}",
            data=json.dumps(updated_data),
            content_type="application/json",
        )

        # Assert response (should be 404 Not Found)
        assert response.status_code == 404

    def test_delete_nonexistent_employee(self, auth_client):
        """Test deleting a non-existent employee"""
        # Use a non-existent ID
        non_existent_id = 99999

        # Send request to delete non-existent employee
        response = auth_client.delete(f"/api/accounts/delete/{non_existent_id}")

        # Assert response (should be 404 Not Found)
        assert response.status_code == 404

    def test_edit_employee_in_different_office(self, auth_client):
        """Test that a master user cannot edit employees from a different office"""
        # Create another office
        other_office = Office.objects.create(
            name="Other Office", address="456 Other St", phone="************"
        )

        # Create an employee in the other office
        other_employee = User.objects.create_user(
            username="other_employee",
            password="password123",
            role=Role.EMPLOYEE,
            office=other_office,
        )

        # Try to edit the employee from another office
        updated_data = {"first_name": "Updated", "last_name": "Name"}

        response = auth_client.put(
            f"/api/accounts/edit/{other_employee.id}",
            data=json.dumps(updated_data),
            content_type="application/json",
        )

        # Assert response (should be 403 Forbidden)
        assert response.status_code == 403

    def test_delete_employee_in_different_office(self, auth_client):
        """Test that a master user cannot delete employees from a different office"""
        # Create another office
        other_office = Office.objects.create(
            name="Other Office", address="456 Other St", phone="************"
        )

        # Create an employee in the other office
        other_employee = User.objects.create_user(
            username="other_employee",
            password="password123",
            role=Role.EMPLOYEE,
            office=other_office,
        )

        # Try to delete the employee from another office
        response = auth_client.delete(f"/api/accounts/delete/{other_employee.id}")

        # Assert response (should be 403 Forbidden)
        assert response.status_code == 403

    def test_create_employee_invalid_data(self, auth_client):
        """Test creating an employee with invalid data"""
        # Missing required field (password)
        invalid_data = {"username": "invalid_employee"}

        # Send request to create employee with invalid data
        response = auth_client.post(
            "/api/accounts/create",
            data=json.dumps(invalid_data),
            content_type="application/json",
        )

        # Assert response (should fail with 400 or similar)
        assert response.status_code >= 400

    def test_edit_employee_invalid_data(self, auth_client):
        """Test editing an employee with invalid data"""
        # First create an employee
        employee_data = {
            "username": "edit_test_employee",
            "password": "password123",
            "first_name": "Test",
            "last_name": "Employee",
            "email": "<EMAIL>",
            "phone": "**********",
            "role": Role.EMPLOYEE,
            "commission_rate": 10.5,
        }

        response = auth_client.post(
            "/api/accounts/create",
            data=json.dumps(employee_data),
            content_type="application/json",
        )

        employee_id = json.loads(response.content)["id"]

        # Invalid commission rate (over 100%)
        invalid_data = {
            "email": 150.0  # Should be between 0 and 100
        }

        # Send request to edit employee with invalid data
        response = auth_client.put(
            f"/api/accounts/edit/{employee_id}",
            data=json.dumps(invalid_data),
            content_type="application/json",
        )

        # Assert response (should fail with 400 or similar)
        assert response.status_code >= 400
