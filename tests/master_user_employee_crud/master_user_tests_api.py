import json
import pytest
from django.test import Client
from accounts.models import User, Role
from offices.models import Office


@pytest.fixture
def office():
    """Create a test office"""
    return Office.objects.create(
        name="Test Office", address="123 Test St", phone="************"
    )


@pytest.fixture
def master_user(office):
    """Create a master user for testing"""
    user = User.objects.create_user(
        username="master",
        password="password123",
        role=Role.MASTER,
        office=office,
        email="<EMAIL>",
    )
    return user


@pytest.fixture
def auth_client(client: Client, master_user):
    """Returns an authenticated client with master user token"""
    # Login to get token
    response = client.post(
        "/api/accounts/",
        query_params={
            "username": "master",
            "password": "password123",
            "office_id": master_user.office.id,
        },
        content_type="application/json",
    )
    token = json.loads(response.content)["token"]

    # Create a new client with authentication headers
    auth_client = Client(headers={"Authorization": f"Bearer {token}"})
    auth_client.defaults["Authorization"] = f"Bearer {token}"
    return auth_client


@pytest.mark.django_db
class TestMasterUserEmployeeWorkflow:
    """Test the complete workflow of master user managing employees"""

    def test_create_employee(self, auth_client: Client, master_user):
        """Test creating a new employee"""
        # Prepare employee data
        employee_data = {
            "username": "employee1",
            "password": "employee123",
            "first_name": "Test",
            "last_name": "Employee",
            "email": "<EMAIL>",
            "phone": "**********",
            "role": Role.EMPLOYEE,
            "commission_rate": 10.5,
        }

        # Send request to create employee
        response = auth_client.post(
            "/api/accounts/create",
            data=json.dumps(employee_data),
            content_type="application/json",
        )

        # Assert response
        assert response.status_code == 200
        response_data = json.loads(response.content)

        # Verify employee was created with correct data
        assert response_data["username"] == employee_data["username"]
        assert response_data["first_name"] == employee_data["first_name"]
        assert response_data["last_name"] == employee_data["last_name"]
        assert response_data["email"] == employee_data["email"]
        assert response_data["phone"] == employee_data["phone"]
        assert response_data["role"] == employee_data["role"]
        assert (
            float(response_data["commission_rate"]) == employee_data["commission_rate"]
        )

        # Verify employee is associated with master's office
        assert response_data["office"]["id"] == master_user.office.id

        # Save employee ID for later tests
        return response_data["id"]

    def test_edit_employee(self, auth_client, master_user):
        """Test editing an existing employee"""
        # First create an employee
        employee_id = self.test_create_employee(auth_client, master_user)

        # Prepare updated data
        updated_data = {
            "first_name": "Updated",
            "last_name": "Name",
            "commission_rate": 15.0,
        }

        # Send request to edit employee
        response = auth_client.put(
            f"/api/accounts/edit/{employee_id}",
            data=json.dumps(updated_data),
            content_type="application/json",
        )

        # Assert response
        assert response.status_code == 200
        response_data = json.loads(response.content)

        # Verify employee was updated with correct data
        assert response_data["first_name"] == updated_data["first_name"]
        assert response_data["last_name"] == updated_data["last_name"]
        assert (
            float(response_data["commission_rate"]) == updated_data["commission_rate"]
        )

    def test_delete_employee(self, auth_client, master_user):
        """Test deleting an employee (soft delete)"""
        # First create an employee
        employee_id = self.test_create_employee(auth_client, master_user)

        # Send request to delete employee
        response = auth_client.delete(f"/api/accounts/delete/{employee_id}")

        # Assert response
        assert response.status_code == 200
        response_data = json.loads(response.content)
        assert response_data["success"] is True

        # Verify employee is soft deleted (is_active=False)
        employee = User.objects.get(id=employee_id)
        assert employee.is_active is False

    def test_list_office_users(self, auth_client, master_user):
        """Test listing all users in the office"""
        # First create an employee
        self.test_create_employee(auth_client, master_user)

        # Send request to list office users
        response = auth_client.get("/api/offices/users")

        # Assert response
        assert response.status_code == 200
        response_data = json.loads(response.content)

        # Should include at least the master user and the created employee
        assert len(response_data) >= 2

        # Verify all users belong to the same office
        for user in response_data:
            assert user["office"]["id"] == master_user.office.id

    def test_unauthorized_access(self, client, office):
        """Test that unauthorized users cannot access protected endpoints"""
        # Create a regular employee (not a master)
        employee = User.objects.create_user(
            username="regular_employee",
            password="password123",
            role=Role.EMPLOYEE,
            office=office,
            email="<EMAIL>",
            phone="**********",
            first_name="Test",
            last_name="Employee",
        )

        # Login as employee
        response = client.post(
            "/api/accounts/",
            query_params={
                "username": "regular_employee",
                "password": "password123",
                "office_id": office.id,
            },
            content_type="application/json",
        )
        token = json.loads(response.content)["token"]

        # Create a client with employee token
        employee_client = Client(headers={"Authorization": f"Bearer {token}"})

        # Try to create a new user (should be forbidden)
        employee_data = {
            "username": "another_employee",
            "password": "password123",
            "role": Role.EMPLOYEE,
            "office_id": office.id,
            "email": "<EMAIL>",
            "phone": "**********",
            "first_name": "Test",
            "last_name": "Employee",
        }

        response = employee_client.post(
            "/api/accounts/create",
            data=json.dumps(employee_data),
            content_type="application/json",
        )

        # Assert forbidden response
        assert response.status_code == 403

        # Try to edit a user (should be forbidden)
        response = employee_client.put(
            f"/api/accounts/edit/{employee.id}",
            data=json.dumps({"first_name": "Updated"}),
            content_type="application/json",
        )

        # Assert forbidden response
        assert response.status_code == 403

        # Try to delete a user (should be forbidden)
        response = employee_client.delete(f"/api/accounts/delete/{employee.id}")

        # Assert forbidden response
        assert response.status_code == 403
