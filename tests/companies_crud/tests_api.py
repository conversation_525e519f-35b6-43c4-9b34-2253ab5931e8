import json
import pytest
from django.test import Client
from accounts.models import User, Role
from offices.models import Office
from orders.models import Company


@pytest.fixture
def office():
    """Create a test office"""
    return Office.objects.create(
        name="Test Office", address="123 Test St", phone="************"
    )


@pytest.fixture
def master_user(office):
    """Create a master user for testing"""
    user = User.objects.create_user(
        username="master",
        password="password123",
        role=Role.MASTER,
        office=office,
        email="<EMAIL>",
    )
    return user


@pytest.fixture
def manager_user(office):
    """Create a manager user for testing"""
    user = User.objects.create_user(
        username="manager",
        password="password123",
        role=Role.MANAGER,
        office=office,
        email="<EMAIL>",
    )
    return user


@pytest.fixture
def employee_user(office):
    """Create an employee user for testing"""
    user = User.objects.create_user(
        username="employee",
        password="password123",
        role=Role.EMPLOYEE,
        office=office,
        email="<EMAIL>",
    )
    return user


@pytest.fixture
def auth_client_master(client: Client, master_user):
    """Returns an authenticated client with master user token"""
    # Login to get token
    response = client.post(
        "/api/accounts/",
        query_params={
            "username": "master",
            "password": "password123",
            "office_id": master_user.office.id,
        },
        content_type="application/json",
    )
    token = json.loads(response.content)["token"]

    # Create a new client with authentication headers
    auth_client = Client(headers={"Authorization": f"Bearer {token}"})
    auth_client.defaults["Authorization"] = f"Bearer {token}"
    return auth_client


@pytest.fixture
def auth_client_manager(client: Client, manager_user):
    """Returns an authenticated client with manager user token"""
    # Login to get token
    response = client.post(
        "/api/accounts/",
        query_params={
            "username": "manager",
            "password": "password123",
            "office_id": manager_user.office.id,
        },
        content_type="application/json",
    )
    token = json.loads(response.content)["token"]

    # Create a new client with authentication headers
    auth_client = Client(headers={"Authorization": f"Bearer {token}"})
    auth_client.defaults["Authorization"] = f"Bearer {token}"
    return auth_client


@pytest.fixture
def auth_client_employee(client: Client, employee_user):
    """Returns an authenticated client with employee user token"""
    # Login to get token
    response = client.post(
        "/api/accounts/",
        query_params={
            "username": "employee",
            "password": "password123",
            "office_id": employee_user.office.id,
        },
        content_type="application/json",
    )
    token = json.loads(response.content)["token"]

    # Create a new client with authentication headers
    auth_client = Client(headers={"Authorization": f"Bearer {token}"})
    auth_client.defaults["Authorization"] = f"Bearer {token}"
    return auth_client


@pytest.mark.django_db
class TestCompaniesWorkflow:
    """Test the complete workflow of managing companies"""

    def test_create_company_as_master(self, auth_client_master, master_user):
        """Test creating a new company as a master user"""
        # Prepare company data
        company_data = {
            "name": "Test Company",
            "address": "456 Company St",
            "phone": "************",
            "color_code": "#FF5733",
            "is_active": True,
        }

        # Send request to create company
        response = auth_client_master.post(
            "/api/companies/",
            data=json.dumps(company_data),
            content_type="application/json",
        )

        # Assert response
        assert response.status_code == 200
        response_data = json.loads(response.content)

        # Verify company was created with correct data
        assert response_data["name"] == company_data["name"]
        assert response_data["address"] == company_data["address"]
        assert response_data["phone"] == company_data["phone"]
        assert response_data["color_code"] == company_data["color_code"]
        assert response_data["is_active"] == company_data["is_active"]

        # Verify company is associated with master's office
        assert response_data["office"]["id"] == master_user.office.id

        # Save company ID for later tests
        return response_data["id"]

    def test_create_company_as_manager(self, auth_client_manager, manager_user):
        """Test creating a new company as a manager user"""
        # Prepare company data
        company_data = {
            "name": "Manager's Company",
            "address": "789 Manager St",
            "phone": "************",
            "color_code": "#33FF57",
            "is_active": True,
        }

        # Send request to create company
        response = auth_client_manager.post(
            "/api/companies/",
            data=json.dumps(company_data),
            content_type="application/json",
        )

        # Assert response
        assert response.status_code == 200
        response_data = json.loads(response.content)

        # Verify company was created with correct data
        assert response_data["name"] == company_data["name"]
        assert response_data["address"] == company_data["address"]
        assert response_data["phone"] == company_data["phone"]
        assert response_data["color_code"] == company_data["color_code"]
        assert response_data["is_active"] == company_data["is_active"]

        # Verify company is associated with manager's office
        assert response_data["office"]["id"] == manager_user.office.id

        # Save company ID for later tests
        return response_data["id"]

    def test_edit_company(self, auth_client_master, master_user):
        """Test editing an existing company"""
        # First create a company
        company_id = self.test_create_company_as_master(auth_client_master, master_user)

        # Prepare updated data
        updated_data = {
            "name": "Updated Company",
            "address": "Updated Address",
            "color_code": "#3366FF",
        }

        # Send request to edit company
        response = auth_client_master.put(
            f"/api/companies/{company_id}/",
            data=json.dumps(updated_data),
            content_type="application/json",
        )

        # Assert response
        assert response.status_code == 200
        response_data = json.loads(response.content)

        # Verify company was updated with correct data
        assert response_data["name"] == updated_data["name"]
        assert response_data["address"] == updated_data["address"]
        assert response_data["color_code"] == updated_data["color_code"]

    def test_delete_company(self, auth_client_master, master_user):
        """Test deleting a company (soft delete)"""
        # First create a company
        company_id = self.test_create_company_as_master(auth_client_master, master_user)

        # Send request to delete company
        response = auth_client_master.delete(f"/api/companies/{company_id}/")

        # Assert response
        assert response.status_code == 200
        response_data = json.loads(response.content)
        assert response_data["success"] is True

        # Verify company is soft deleted (is_active=False)
        company = Company.objects.get(id=company_id)
        assert company.is_active is False

    def test_list_companies(self, auth_client_master, master_user):
        """Test listing all companies in the office"""
        # First create a company
        self.test_create_company_as_master(auth_client_master, master_user)

        # Send request to list companies
        response = auth_client_master.get("/api/companies")

        # Assert response
        assert response.status_code == 200
        response_data = json.loads(response.content)

        # Should include at least the created company
        assert len(response_data) >= 1

        # Verify all companies belong to the same office
        for company in response_data:
            assert company["office"]["id"] == master_user.office.id

    def test_unauthorized_access(self, auth_client_employee):
        """Test that unauthorized users (employees) cannot access protected endpoints"""
        # Try to create a new company (should be forbidden)
        company_data = {
            "name": "Employee Company",
            "address": "123 Employee St",
            "phone": "************",
            "color_code": "#FF5733",
        }

        response = auth_client_employee.post(
            "/api/companies/",
            data=json.dumps(company_data),
            content_type="application/json",
        )

        # Assert forbidden response
        assert response.status_code == 403
