import json
import pytest
from django.test import Client
from accounts.models import User, Role
from offices.models import Office
from orders.models import Company


@pytest.fixture
def office():
    """Create a test office"""
    return Office.objects.create(
        name="Test Office", address="123 Test St", phone="************"
    )


@pytest.fixture
def other_office():
    """Create another test office"""
    return Office.objects.create(
        name="Other Office", address="456 Other St", phone="************"
    )


@pytest.fixture
def master_user(office):
    """Create a master user for testing"""
    user = User.objects.create_user(
        username="master",
        password="password123",
        role=Role.MASTER,
        office=office,
        email="<EMAIL>",
    )
    return user


@pytest.fixture
def auth_client(client: Client, master_user):
    """Returns an authenticated client with master user token"""
    # Login to get token
    response = client.post(
        "/api/accounts/",
        query_params={
            "username": "master",
            "password": "password123",
            "office_id": master_user.office.id,
        },
        content_type="application/json",
    )
    token = json.loads(response.content)["token"]

    # Create a new client with authentication headers
    auth_client = Client(headers={"Authorization": f"Bearer {token}"})
    auth_client.defaults["Authorization"] = f"Bearer {token}"
    return auth_client


@pytest.mark.django_db
class TestCompaniesEdgeCases:
    """Test edge cases for managing companies"""

    def test_create_company_duplicate_name(self, auth_client):
        """Test creating a company with a duplicate name in the same office"""
        # First create a company
        company_data = {
            "name": "Unique Company",
            "address": "123 Company St",
            "phone": "************",
            "color_code": "#FF5733",
        }

        # Create first company
        auth_client.post(
            "/api/companies",
            data=json.dumps(company_data),
            content_type="application/json",
        )

        # Try to create another company with the same name
        # Note: The API might allow duplicate names, so this test might need adjustment
        # based on the actual business requirements
        response = auth_client.post(
            "/api/companies/",
            data=json.dumps(company_data),
            content_type="application/json",
        )

        # If duplicate names are allowed, this should succeed (200)
        # If not allowed, this should fail (400 or similar)
        # For this test, we'll assume duplicates are allowed but you can adjust as needed
        assert response.status_code == 200

    def test_edit_nonexistent_company(self, auth_client):
        """Test editing a non-existent company"""
        # Use a non-existent ID
        non_existent_id = 99999

        # Prepare updated data
        updated_data = {"name": "Updated Company", "address": "Updated Address"}

        # Send request to edit non-existent company
        response = auth_client.put(
            f"/api/companies/{non_existent_id}/",
            data=json.dumps(updated_data),
            content_type="application/json",
        )

        # Assert response (should be 404 Not Found)
        assert response.status_code == 404

    def test_delete_nonexistent_company(self, auth_client):
        """Test deleting a non-existent company"""
        # Use a non-existent ID
        non_existent_id = 99999

        # Send request to delete non-existent company
        response = auth_client.delete(f"/api/companies/{non_existent_id}/")

        # Assert response (should be 404 Not Found)
        assert response.status_code == 404

    def test_edit_company_in_different_office(self, auth_client, other_office):
        """Test that a user cannot edit companies from a different office"""
        # Create a company in the other office
        other_company = Company.objects.create(
            name="Other Office Company",
            office=other_office,
            address="789 Other Office St",
            phone="************",
            color_code="#FF5733",
        )

        # Try to edit the company from another office
        updated_data = {"name": "Updated Company", "address": "Updated Address"}

        response = auth_client.put(
            f"/api/companies/{other_company.id}/",
            data=json.dumps(updated_data),
            content_type="application/json",
        )

        # Assert response (should be 404 Not Found since get_object_or_404 filters by office)
        assert response.status_code == 404

    def test_delete_company_in_different_office(self, auth_client, other_office):
        """Test that a user cannot delete companies from a different office"""
        # Create a company in the other office
        other_company = Company.objects.create(
            name="Other Office Company",
            office=other_office,
            address="789 Other Office St",
            phone="************",
            color_code="#FF5733",
        )

        # Try to delete the company from another office
        response = auth_client.delete(f"/api/companies/{other_company.id}/")

        # Assert response (should be 404 Not Found since get_object_or_404 filters by office)
        assert response.status_code == 404

    def test_create_company_invalid_data(self, auth_client):
        """Test creating a company with invalid data"""
        # Empty name (required field)
        invalid_data = {"name": ""}

        # Send request to create company with invalid data
        response = auth_client.post(
            "/api/companies/",
            data=json.dumps(invalid_data),
            content_type="application/json",
        )

        # Assert response (should fail with 400 or similar)
        assert response.status_code >= 400

    def test_edit_company_invalid_data(self, auth_client):
        """Test editing a company with invalid data"""
        # First create a company
        company_data = {
            "name": "Edit Test Company",
            "address": "123 Edit St",
            "phone": "************",
            "color_code": "#FF5733",
        }

        response = auth_client.post(
            "/api/companies/",
            data=json.dumps(company_data),
            content_type="application/json",
        )

        company_id = json.loads(response.content)["id"]

        # Empty name (required field)
        invalid_data = {"name": ""}

        # Send request to edit company with invalid data
        response = auth_client.put(
            f"/api/companies/{company_id}/",
            data=json.dumps(invalid_data),
            content_type="application/json",
        )

        # Assert response (should fail with 400 or similar)
        assert response.status_code >= 400

    def test_list_companies_after_delete(self, auth_client):
        """Test that deleted companies don't appear in the list"""
        # Create a company
        company_data = {
            "name": "Delete Test Company",
            "address": "123 Delete St",
            "phone": "************",
            "color_code": "#FF5733",
        }

        response = auth_client.post(
            "/api/companies/",
            data=json.dumps(company_data),
            content_type="application/json",
        )

        company_id = json.loads(response.content)["id"]

        # Delete the company
        auth_client.delete(f"/api/companies/{company_id}/")

        # List companies
        response = auth_client.get("/api/companies")
        companies = json.loads(response.content)

        # Verify the deleted company is not in the list
        deleted_company_found = False
        for company in companies:
            if company["id"] == company_id:
                deleted_company_found = True
                break

        assert not deleted_company_found, (
            "Deleted company should not appear in the list"
        )
