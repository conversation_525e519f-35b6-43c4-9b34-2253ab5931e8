[project]
name = "unship-v3-backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "colorlog>=6.9.0",
    "django>=5.2.1",
    "django-cacheops>=7.2",
    "django-cors-headers>=4.7.0",
    "django-ninja>=1.4.1",
    "django-redis>=5.4.0",
    "ninja-schema>=0.14.2",
    "pillow>=11.2.1",
    "pyjwt>=2.10.1",
    "pytest>=8.3.5",
    "pytest-django>=4.11.1",
    "sentry-sdk[django]>=2.28.0",
]
