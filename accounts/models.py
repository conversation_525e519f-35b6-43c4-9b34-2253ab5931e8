from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from offices.models import Office
from django.utils import timezone
from datetime import datetime, timezone


class Role(models.TextChoices):
    MASTER = "MASTER", _("Master User")
    MANAGER = "MANAGER", _("Office Manager")
    EMPLOYEE = "EMPLOYEE", _("Delivery Employee")
    CUSTOM_USER = "CUSTOM_USER", _("Custom User")


class User(AbstractUser):
    """
    Custom user model extending Django's AbstractUser.
    Includes additional fields for user role and office association.
    """

    role = models.CharField(max_length=16, choices=Role.choices, default=Role.EMPLOYEE)
    office = models.ForeignKey(
        Office, on_delete=models.PROTECT, related_name="users", null=True, blank=True
    )
    phone = models.Char<PERSON>ield(max_length=20, blank=True)
    is_active = models.BooleanField(default=True)

    # Additional fields for delivery employees
    commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        null=True,
        blank=True,
        help_text=_("Commission rate percentage for delivery employees"),
    )
    current_location_lat = models.DecimalField(
        max_digits=9, decimal_places=6, null=True, blank=True
    )
    current_location_lng = models.DecimalField(
        max_digits=9, decimal_places=6, null=True, blank=True
    )
    last_location_update = models.DateTimeField(null=True, blank=True)

    # total money deserved
    balance = models.FloatField(default=0)

    class Meta:
        db_table = "users"
        verbose_name = _("User")
        verbose_name_plural = _("Users")
        indexes = [
            models.Index(fields=["role"]),
            models.Index(fields=["office", "role"]),
        ]

    def __str__(self):
        return f"{self.get_full_name()} ({self.get_role_display()})"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

    # soft delete
    def soft_delete(self):
        self.is_active = False
        self.save()

    # restore
    def restore(self):
        self.is_active = True
        self.save()

    # update location
    def update_location(self, lat, lng):
        self.current_location_lat = lat
        self.current_location_lng = lng
        self.last_location_update = datetime.now(timezone.utc)
        self.save()


class TransactionType(models.TextChoices):
    DEPOSIT = "DEPOSIT", _("Deposit")
    WITHDRAW = "WITHDRAW", _("Withdraw")
    TRANSFER = "TRANSFER", _("Transfer")
    COMMISSION = "COMMISSION", _("Commission")
    PAYMENT = "PAYMENT", _("Payment")
    RETURN = "RETURN", _("Return")
    OTHER = "OTHER", _("Other")
    SALARY = "SALARY", _("Salary")
    SETTLEMENT = (
        "SETTLEMENT",
        _("Settlement"),
    )  # when employee is settled with the office (return to zero balance)


class EmployeeBalanceTransaction(models.Model):
    employee = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="balance_transactions"
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    transaction_type = models.CharField(max_length=10, choices=TransactionType.choices)
    balance_before = models.DecimalField(max_digits=10, decimal_places=2)
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="created_balance_transactions"
    )
    order = models.ForeignKey(
        "orders.Order",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="employee_balance_transactions",
    )
    notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = "employee_balance_transactions"
        verbose_name = _("Employee Balance Transaction")
