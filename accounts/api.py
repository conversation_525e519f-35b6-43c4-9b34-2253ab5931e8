from datetime import datetime
from typing import List, Optional
from django.shortcuts import get_object_or_404
from django.db.models import Sum
from django.db import IntegrityError
from ninja import Router
from uuid import uuid4
from django.core.cache import cache

from accounts.models import EmployeeBalanceTransaction, TransactionType, User, Role
from accounts.schemas import (
    LoginOutSchema,
    UserCreateSchema,
    UserEditSchema,
    UserModelSchema,
    LocationUpdateSchema,
)
from accounts.utils import is_master_user
from core import settings
from core.middleware import AuthBearer
from core.utils import JWTPayload
from core.errors import HttpError
from orders.models import Order, OrderHandlingStatus
from .schemas import (
    EmployeeBalanceTransactionCreateSchema,
    EmployeeBalanceTransactionSchema,
    EmployeeStatsResponseSchema,
    QRCodeGenerateResponseSchema,
    QRCodeGenerateSchema,
    QRCodeLoginSchema,
)
from core.schemas import SuccessResponse

# /accounts/
accountsApi = Router()

# Constants
QR_TOKEN_EXPIRATION_SECONDS = 60 * 5  # 5 minutes


# /accounts/login/
@accountsApi.post("/", response=LoginOutSchema)
def login(request, phone: str, password: str, office_id: int):
    """
    Login to the system and retrun token and user info
    """
    user = User.objects.filter(phone=phone, office_id=office_id).first()
    if not user:
        raise HttpError(401, "Invalid phone or password", "UNAUTHORIZED")
    if not user.check_password(password):
        raise HttpError(401, "Invalid phone or password", "UNAUTHORIZED")
    token = JWTPayload(settings.SECRET_KEY).from_user(user)
    return {"token": token, "user": UserModelSchema.from_orm(user)}


# /accounts/me/
@accountsApi.get("/me", auth=AuthBearer(), response=UserModelSchema)
def get_me(request):
    """
    Get the current user
    """
    user = request.auth
    return UserModelSchema.from_orm(user)


# /accounts/create/
@accountsApi.post("/create", auth=AuthBearer(), response=UserModelSchema)
def create_user(request, payload: UserCreateSchema):
    if not is_master_user(request):
        raise HttpError(
            403, "Forbidden: Only Master Users can create users.", "FORBIDDEN"
        )

    # Create user data with master user's office
    user_data = payload.dict()

    # see if there is phone number with same office
    if user_data.get("phone"):
        phone = user_data.get("phone")
        if User.objects.filter(phone=phone, office_id=request.auth.office_id).exists():
            raise HttpError(
                400, "Phone number already exists in this office", "BAD_REQUEST"
            )

    # see if there is email with same office
    if user_data.get("email"):
        email = user_data.get("email")
        if User.objects.filter(email=email, office_id=request.auth.office_id).exists():
            raise HttpError(400, "Email already exists in this office", "BAD_REQUEST")

    if user_data.get("username"):
        username = user_data.get("username")
        if User.objects.filter(
            username=username, office_id=request.auth.office_id
        ).exists():
            raise HttpError(
                400, "Username already exists in this office", "BAD_REQUEST"
            )

    if user_data.get("email") == "":
        user_data["email"] = None

    try:
        user = User.objects.create_user(**user_data)
        user.office = request.auth.office
        user.save()
    # handle username already exists
    except IntegrityError as e:
        error_message = str(e)
        if "username" in error_message:
            raise HttpError(400, "Username already exists", "BAD_REQUEST")
        elif "email" in error_message:
            raise HttpError(400, "Email already exists", "BAD_REQUEST")
        else:
            raise HttpError(
                400, "A field with this value already exists", "BAD_REQUEST"
            )
    except Exception as e:
        raise HttpError(400, str(e), "BAD_REQUEST")
    return UserModelSchema.from_orm(user)


# /accounts/edit/{user_id}/
@accountsApi.put("/edit/{user_id}", auth=AuthBearer(), response=UserModelSchema)
def edit_user(request, user_id: int, payload: UserEditSchema):
    if not is_master_user(request):
        raise HttpError(
            403, "Forbidden: Only Master Users can edit users.", "FORBIDDEN"
        )

    # Get the user to edit
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        raise HttpError(404, "User not found", "NOT_FOUND")

    # Ensure the user belongs to the master user's office
    if user.office_id != request.auth.office_id:
        raise HttpError(
            403, "Forbidden: You can only edit users in your own office.", "FORBIDDEN"
        )

    for attr, value in payload.dict(exclude_unset=True).items():
        setattr(user, attr, value)
    user.save()
    return UserModelSchema.from_orm(user)


# /accounts/delete/{user_id}/
@accountsApi.delete("/delete/{user_id}", auth=AuthBearer(), response=SuccessResponse)
def delete_user(request, user_id: int):
    if not is_master_user(request):
        raise HttpError(
            403, "Forbidden: Only Master Users can delete users.", "FORBIDDEN"
        )

    # Get the user to delete
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        raise HttpError(404, "User not found", "NOT_FOUND")

    # Ensure the user belongs to the master user's office
    if user.office_id != request.auth.office_id:
        raise HttpError(
            403, "Forbidden: You can only delete users in your own office.", "FORBIDDEN"
        )

    # Instead of user.delete(), implement soft delete
    user.is_active = False
    user.save()

    return {"success": True}


# /accounts/update-location/
@accountsApi.post("/update-location", auth=AuthBearer(), response=UserModelSchema)
def update_location(request, payload: LocationUpdateSchema):
    """
    Update the current user's location coordinates
    """
    # Add validation for reasonable lat/lng values
    if not (-90 <= payload.lat <= 90) or not (-180 <= payload.lng <= 180):
        raise HttpError(400, "Invalid latitude or longitude values", "BAD_REQUEST")

    user: User = request.auth
    user.update_location(payload.lat, payload.lng)
    return UserModelSchema.from_orm(user)


@accountsApi.get(
    "/employees/{employee_id}/stats/",
    response=EmployeeStatsResponseSchema,
    auth=AuthBearer(),
)
def get_employee_stats(
    request,
    employee_id: int,
    date_from: Optional[datetime] = None,
    date_to: Optional[datetime] = None,
):
    """
    Get statistics for an employee within a specific date range
    """
    employee = get_object_or_404(User, id=employee_id, role=Role.EMPLOYEE)

    # Filter orders by date range
    orders_query = Order.objects.filter(assigned_to=employee)

    if date_from:
        orders_query = orders_query.filter(created_at__gte=date_from)
    else:
        # get last settled date
        last_settled_date = (
            EmployeeBalanceTransaction.objects.filter(
                employee=employee,
                transaction_type=TransactionType.SETTLEMENT,
            )
            .order_by("-created_at")
            .first()
        )
        if last_settled_date:
            orders_query = orders_query.filter(
                created_at__gte=last_settled_date.created_at
            )
    if date_to:
        orders_query = orders_query.filter(created_at__lte=date_to)

    # Calculate statistics
    total_orders_assigned = orders_query.count()

    completed_orders = orders_query.filter(
        order_handling_status=OrderHandlingStatus.COMPLETED
    )
    total_orders_completed = completed_orders.count()

    # Calculate financial metrics
    total_money_to_collect = (
        orders_query.aggregate(total=Sum("total_price", default=0))["total"] or 0
    )

    total_money_collected = (
        completed_orders.aggregate(total=Sum("delivery_customer_payment", default=0))[
            "total"
        ]
        or 0
    )

    # Calculate money deserved (this would depend on your commission system)
    # For now, let's assume a simple calculation - you can adjust based on your business logic
    total_money_deserved = float(employee.balance)

    return {
        "total_money_deserved": total_money_deserved,
        "total_orders_completed": total_orders_completed,
        "total_orders_assigned": total_orders_assigned,
        "total_money_to_collect": total_money_to_collect,
        "total_money_collected": total_money_collected,
    }


@accountsApi.get(
    "/employees/{employee_id}/balance-transactions/",
    response=List[EmployeeBalanceTransactionSchema],
    auth=AuthBearer(),
)
def get_employee_balance_transactions(request, employee_id: int):
    employee = get_object_or_404(User, id=employee_id, role=Role.EMPLOYEE)
    return employee.balance_transactions.all()


@accountsApi.post(
    "/employees/{employee_id}/settle/",
    response=EmployeeBalanceTransactionSchema,
    auth=AuthBearer(),
)
def settle_employee_balance(request, employee_id: int):
    if (
        not is_master_user(request) and request.auth.role == Role.MANAGER
    ) and request.auth.id != employee_id:
        raise HttpError(
            403,
            "Forbidden: Only Master Users or Managers can settle employee balances.",
            "FORBIDDEN",
        )

    employee = get_object_or_404(User, id=employee_id, role=Role.EMPLOYEE)
    employee.balance = 0
    employee.save()
    EmployeeBalanceTransaction.objects.create(
        employee=employee,
        amount=0,
        transaction_type=TransactionType.SETTLEMENT,
        created_by=request.auth,
        balance_before=employee.balance,
        notes="Settled with the office",
        order=None,
    )
    return {"success": True}


@accountsApi.post(
    "/qr/generate", auth=AuthBearer(), response=QRCodeGenerateResponseSchema
)
def generate_qr_code(request, payload: QRCodeGenerateSchema):
    """Generate a time-limited, single-use QR token that can be used for password-less login.

    Only users with the `MASTER` role are allowed to generate QR codes. The generated
    token is stored in Redis (via Django cache) with a short TTL to prevent reuse.
    """
    if not is_master_user(request):
        raise HttpError(
            403, "Forbidden: Only Master Users can generate QR codes.", "FORBIDDEN"
        )

    # Validate target employee
    employee = get_object_or_404(User, id=payload.employee_id)

    if employee.office_id != request.auth.office_id:
        raise HttpError(
            403,
            "You can only generate QR codes for employees in your office",
            "FORBIDDEN",
        )

    # Create a cryptographically-secure random token
    qr_token = uuid4().hex

    # Persist in cache so it can be redeemed exactly once
    cache_key = f"qr_login:{qr_token}"
    cache.set(
        cache_key,
        {
            "office_id": employee.office_id,
            "employee_id": employee.id,
            "generated_by": request.auth.id,
        },
        timeout=QR_TOKEN_EXPIRATION_SECONDS,
    )

    return {"qr_token": qr_token, "expires_in": QR_TOKEN_EXPIRATION_SECONDS}


@accountsApi.post("/qr/login", response=LoginOutSchema)
def login_with_qr(request, payload: QRCodeLoginSchema):
    """Authenticate a user using a QR token.

    The user provides only the QR token obtained by scanning the code generated for
    them by a master user. The token is validated for:
    1. Existence and not being expired (cache lookup).
    2. Belonging to a valid employee.

    Upon successful validation the token is deleted to enforce single-use and the
    user receives a regular JWT token identical to the standard login flow.
    """
    cache_key = f"qr_login:{payload.qr_token}"
    qr_payload = cache.get(cache_key)

    if qr_payload is None:
        raise HttpError(400, "Invalid or expired QR token", "INVALID_QR_TOKEN")

    employee_id = qr_payload.get("employee_id")
    user = User.objects.filter(id=employee_id).first()
    if not user:
        # Possible that employee was deleted after token generation
        cache.delete(cache_key)
        raise HttpError(404, "Employee not found", "NOT_FOUND")

    if not user.is_active:
        raise HttpError(403, "User account is inactive", "FORBIDDEN")

    # Invalidate token to prevent reuse
    cache.delete(cache_key)

    jwt_token = JWTPayload(settings.SECRET_KEY).from_user(user)

    return {"token": jwt_token, "user": UserModelSchema.from_orm(user)}
