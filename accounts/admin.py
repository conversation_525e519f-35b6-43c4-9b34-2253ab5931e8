from django.contrib import admin
from .models import User
from django.contrib.auth.admin import UserAdmin


class UserAdmin(UserAdmin):
    list_display = ("email", "first_name", "last_name", "is_staff")
    search_fields = ("email", "first_name", "last_name")
    ordering = ("email",)

    fieldsets = (
        (None, {"fields": ("username", "email", "password")}),
        ("Personal info", {"fields": ("first_name", "last_name")}),
        (
            "Permissions",
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_superuser",
                    "groups",
                    "user_permissions",
                )
            },
        ),
        ("Important dates", {"fields": ("last_login", "date_joined")}),
        (
            "Custom fields",
            {
                "fields": (
                    "role",
                    "office",
                    "phone",
                    "commission_rate",
                    "current_location_lat",
                    "current_location_lng",
                    "last_location_update",
                    "balance",
                )
            },
        ),
    )


admin.site.register(User, UserAdmin)
