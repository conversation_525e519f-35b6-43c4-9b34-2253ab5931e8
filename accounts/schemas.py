from ninja import Schema, Field
from ninja_schema import ModelSchema
from datetime import datetime
from typing import Optional

from pydantic import EmailStr

from accounts.models import TransactionType, User, Role, EmployeeBalanceTransaction


class UserModelSchema(ModelSchema):
    class Config:
        model = User
        depth = 2
        include = "__all__"
        exclude = [
            "password",
            "last_login",
            "is_superuser",
            "is_staff",
            "is_active",
            "groups",
            "user_permissions",
            "balance",
        ]


class LoginOutSchema(Schema):
    token: str
    user: UserModelSchema


class UserCreateSchema(Schema):
    username: str
    password: str
    first_name: str = None
    last_name: str = None
    email: Optional[str] = None
    phone: str = Field(..., min_length=10)
    role: Role = Role.EMPLOYEE
    commission_rate: Optional[float] = None


class UserEditSchema(Schema):
    username: str = Field(None, min_length=3)
    first_name: str = Field(None, min_length=3)
    last_name: str = Field(None, min_length=3)
    email: EmailStr = None
    phone: str = Field(None, min_length=10)
    role: Role = None
    commission_rate: float = Field(None, ge=0, le=100)


class LocationUpdateSchema(Schema):
    lat: float
    lng: float


class EmployeeStatsResponseSchema(Schema):
    total_money_deserved: float
    total_orders_completed: int
    total_orders_assigned: int
    total_money_to_collect: float
    total_money_collected: float


class EmployeeBalanceTransactionSchema(ModelSchema):
    class Config:
        model = EmployeeBalanceTransaction
        depth = 2
        include = "__all__"
        exclude = ["deleted_at"]


class EmployeeBalanceTransactionCreateSchema(Schema):
    amount: float
    transaction_type: TransactionType
    order: int
    notes: str
    created_by: int


class QRCodeGenerateResponseSchema(Schema):
    """Response schema for QR code generation"""

    qr_token: str
    expires_in: int = Field(
        300, description="Validity period of the QR token in seconds"
    )


class QRCodeGenerateSchema(Schema):
    """Payload for generating a QR code for a specific employee"""

    employee_id: int


class QRCodeLoginSchema(Schema):
    """Input schema for logging-in with a QR code"""

    qr_token: str
