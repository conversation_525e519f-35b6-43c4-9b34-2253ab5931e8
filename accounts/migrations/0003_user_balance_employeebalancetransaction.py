# Generated by Django 5.2.1 on 2025-06-08 01:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_rename_accounts_us_role_1fa9a5_idx_users_role_0ace22_idx_and_more'),
        ('orders', '0008_order_special_commission_rate'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='balance',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.CreateModel(
            name='EmployeeBalanceTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('transaction_type', models.CharField(choices=[('DEPOSIT', 'Deposit'), ('WITHDRAW', 'Withdraw'), ('TRANSFER', 'Transfer'), ('COMMISSION', 'Commission'), ('PAYMENT', 'Payment'), ('RETURN', 'Return'), ('OTHER', 'Other'), ('SALARY', 'Salary'), ('SETTLEMENT', 'Settlement')], max_length=10)),
                ('balance_before', models.DecimalField(decimal_places=2, max_digits=10)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_balance_transactions', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='balance_transactions', to=settings.AUTH_USER_MODEL)),
                ('order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='employee_balance_transactions', to='orders.order')),
            ],
            options={
                'verbose_name': 'Employee Balance Transaction',
                'db_table': 'employee_balance_transactions',
            },
        ),
    ]
