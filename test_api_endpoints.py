#!/usr/bin/env python3
"""
Test the actual API endpoints to debug the empty file issue.
"""

import sys
import os
import json
from datetime import date

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_bulk_export_api():
    """Test the bulk export API endpoint directly."""
    print("=== Testing Bulk Export API ===")
    
    try:
        # Import Django and setup
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from django.test import RequestFactory
        from orders.api import export_orders_bulk
        from orders.excel_export_schemas import BulkExportRequest
        from accounts.models import User
        
        # Get a test user
        user = User.objects.first()
        if not user:
            print("❌ No users found")
            return False
        
        print(f"👤 Using user: {user.username} (Office: {user.office.name})")
        
        # Create test request payloads
        test_cases = [
            {
                "name": "Empty filters",
                "payload": {
                    "export_type": "bulk",
                    "filters": {},
                    "limit": 10
                }
            },
            {
                "name": "With filename",
                "payload": {
                    "export_type": "bulk",
                    "filters": {},
                    "filename": "test_export.xlsx",
                    "limit": 5
                }
            },
            {
                "name": "With status filter",
                "payload": {
                    "export_type": "bulk",
                    "filters": {
                        "status": ["PENDING", "COMPLETED"]
                    },
                    "limit": 10
                }
            }
        ]
        
        factory = RequestFactory()
        
        for test_case in test_cases:
            print(f"\n🔍 Testing: {test_case['name']}")
            
            try:
                # Create request
                request = factory.post(
                    '/orders/export-excel/bulk',
                    data=json.dumps(test_case['payload']),
                    content_type='application/json'
                )
                request.auth = user
                
                # Create payload object
                payload = BulkExportRequest(**test_case['payload'])
                
                # Call the API function directly
                response = export_orders_bulk(request, payload)
                
                print(f"   ✅ API call successful")
                print(f"   Response type: {type(response)}")
                print(f"   Content-Type: {response.get('Content-Type', 'Not set')}")
                print(f"   Content-Disposition: {response.get('Content-Disposition', 'Not set')}")
                print(f"   Content-Length: {response.get('Content-Length', 'Not set')}")
                
                # Check if it's a FileResponse
                from django.http import FileResponse
                if isinstance(response, FileResponse):
                    print(f"   ✅ FileResponse created successfully")
                    
                    # Try to read some content
                    try:
                        content_start = response.file_to_stream.read(100)
                        response.file_to_stream.seek(0)  # Reset for actual download
                        
                        if content_start:
                            print(f"   ✅ File has content (first 100 bytes): {len(content_start)} bytes")
                        else:
                            print(f"   ❌ File appears to be empty")
                    except Exception as e:
                        print(f"   ⚠️  Could not read file content: {e}")
                else:
                    print(f"   ❌ Not a FileResponse: {response}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing bulk export API: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_preview_api():
    """Test the preview API to see what orders would be exported."""
    print("\n=== Testing Preview API ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from django.test import RequestFactory
        from orders.api import preview_export
        from orders.excel_export_schemas import BulkExportRequest
        from accounts.models import User
        
        user = User.objects.first()
        
        # Test preview with empty filters
        payload_data = {
            "export_type": "bulk",
            "filters": {},
            "limit": 10
        }
        
        factory = RequestFactory()
        request = factory.post(
            '/orders/export-excel/preview',
            data=json.dumps(payload_data),
            content_type='application/json'
        )
        request.auth = user
        
        payload = BulkExportRequest(**payload_data)
        
        result = preview_export(request, payload)
        
        print(f"✅ Preview API successful")
        print(f"   Total orders: {result['total_orders']}")
        print(f"   Sample orders: {len(result['sample_orders'])}")
        print(f"   Estimated size: {result['estimated_file_size_mb']} MB")
        
        if result['sample_orders']:
            print(f"   First order: {result['sample_orders'][0]['customer_name']} (ID: {result['sample_orders'][0]['id']})")
        
        return result['total_orders'] > 0
        
    except Exception as e:
        print(f"❌ Error testing preview API: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_direct_export_service():
    """Test the export service directly with the same data the API would use."""
    print("\n=== Testing Direct Export Service ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        from orders.excel_export_service import export_orders_to_excel, generate_representative_name
        from orders.excel_export_schemas import apply_filters_to_queryset, OrderExportFilters
        from accounts.models import User
        
        user = User.objects.first()
        
        # Simulate the exact same logic as the API
        print("🔍 Simulating API logic:")
        
        # Step 1: Base queryset
        orders = Order.objects.filter(office=user.office).select_related(
            "customer_company", "assigned_to", "order_delivery_status"
        )
        print(f"   Base orders: {orders.count()}")
        
        # Step 2: Apply filters (empty)
        filters = OrderExportFilters()
        orders = apply_filters_to_queryset(orders, filters)
        print(f"   After filters: {orders.count()}")
        
        # Step 3: Apply limit
        limit = 10
        orders = orders[:limit]
        print(f"   After limit: {orders.count()}")
        
        # Step 4: Check exists
        if not orders.exists():
            print("   ❌ No orders exist!")
            return False
        
        # Step 5: Generate rep name
        rep_name = generate_representative_name(user)
        print(f"   Representative: {rep_name}")
        
        # Step 6: Export
        file_path = export_orders_to_excel(
            orders=orders,
            representative_name=rep_name,
            export_date=date.today(),
            filename="direct_test.xlsx"
        )
        
        # Step 7: Check file
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"   ✅ File created: {file_size:,} bytes")
            
            # Check content
            import openpyxl
            wb = openpyxl.load_workbook(file_path)
            ws = wb.active
            
            # Check for data in row 7
            row_7_data = [ws.cell(row=7, column=col).value for col in range(1, 9)]
            has_data = any(cell is not None and str(cell).strip() for cell in row_7_data)
            
            print(f"   Data check: {'✅ Has data' if has_data else '❌ No data'}")
            if has_data:
                print(f"   Row 7: {row_7_data}")
            
            wb.close()
            return has_data
        else:
            print(f"   ❌ File not created")
            return False
        
    except Exception as e:
        print(f"❌ Error testing direct export service: {e}")
        import traceback
        traceback.print_exc()
        return False


def debug_request_payload():
    """Debug what happens with different request payloads."""
    print("\n=== Debugging Request Payloads ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.excel_export_schemas import BulkExportRequest, OrderExportFilters
        
        # Test different payload formats that might come from frontend
        test_payloads = [
            {
                "name": "Minimal payload",
                "data": {
                    "export_type": "bulk"
                }
            },
            {
                "name": "Empty filters object",
                "data": {
                    "export_type": "bulk",
                    "filters": {}
                }
            },
            {
                "name": "Null filters",
                "data": {
                    "export_type": "bulk",
                    "filters": None
                }
            },
            {
                "name": "With limit only",
                "data": {
                    "export_type": "bulk",
                    "limit": 1000
                }
            },
            {
                "name": "Frontend-like payload",
                "data": {
                    "export_type": "bulk",
                    "filters": {
                        "status": [],
                        "company_ids": [],
                        "assigned_to_ids": []
                    },
                    "filename": "hello.xlsx",
                    "limit": 1000
                }
            }
        ]
        
        for test in test_payloads:
            print(f"\n🔍 Testing: {test['name']}")
            print(f"   Payload: {test['data']}")
            
            try:
                # Try to create BulkExportRequest
                request = BulkExportRequest(**test['data'])
                print(f"   ✅ Valid request created")
                print(f"   Filters: {request.filters}")
                print(f"   Limit: {request.limit}")
                print(f"   Filename: {request.filename}")
                
                # Check if filters are empty
                filters_dict = request.filters.model_dump(exclude_none=True)
                print(f"   Filters dict: {filters_dict}")
                
            except Exception as e:
                print(f"   ❌ Invalid request: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging payloads: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Starting API Endpoint Tests\n")
    
    tests = [
        ("Request Payload Debug", debug_request_payload),
        ("Preview API", test_preview_api),
        ("Direct Export Service", test_direct_export_service),
        ("Bulk Export API", test_bulk_export_api),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'='*60}")
    
    # Summary
    print(f"\n🏁 Test Results Summary:")
    print(f"{'='*60}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! The API should be working correctly.")
    else:
        print("⚠️  Some tests failed. Check the specific errors above.")


if __name__ == "__main__":
    main()
