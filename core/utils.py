import jwt

from accounts.models import User


class JWTPayload:
    user_id: int
    office_id: int

    def __init__(self, secret_key: str):
        self.secret_key = secret_key

    def encode(self, payload: dict):
        return jwt.encode(payload, self.secret_key, algorithm="HS256")

    def decode(self, token: str):
        return jwt.decode(token, self.secret_key, algorithms=["HS256"])

    def from_user(self, user: User):
        return self.encode({"user_id": user.id, "office_id": user.office_id})

    def to_user(self, token: str):
        payload = self.decode(token)
        return User.objects.get(id=payload["user_id"])
