from time import time
from ninja.security import HttpBearer

from accounts.models import User
from core import settings
from core.utils import JWTPayload


class AuthBearer(HttpBearer):
    def authenticate(self, request, token) -> User | None:
        try:
            payload = JWTPayload(settings.SECRET_KEY).to_user(token)
            return payload
        except Exception as _:
            return None


class StatsMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        start_time = time()

        response = self.get_response(request)

        duration = time() - start_time

        # Add the header. Or do other things, my use case is to send a monitoring metric
        response["X-Page-Generation-Duration-ms"] = int(duration * 1000)
        print(f"Page generation duration: {round(duration * 1000, 2)} ms")
        return response
