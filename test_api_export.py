#!/usr/bin/env python3
"""
Test script to simulate the exact API export call and debug empty file issue.
"""

import sys
import os
from datetime import date

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_api_export():
    """Test the exact API export logic."""
    print("=== Testing API Export Logic ===")
    
    try:
        # Import Django and setup
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        from orders.excel_export_service import export_orders_to_excel, generate_representative_name
        from orders.excel_export_schemas import apply_filters_to_queryset, OrderExportFilters
        from accounts.models import User
        
        # Simulate user
        user = User.objects.first()
        if not user:
            print("❌ No users found")
            return False
        
        print(f"👤 Using user: {user.username} (Office: {user.office.name if user.office else 'None'})")
        
        # Simulate the exact API logic
        print("\n🔍 Step 1: Base QuerySet")
        orders = Order.objects.filter(office=user.office).select_related(
            "customer_company", "assigned_to", "order_delivery_status"
        )
        print(f"   Base orders count: {orders.count()}")
        
        print("\n🔍 Step 2: Apply Filters")
        # Create empty filters (like a bulk export with no filters)
        filters = OrderExportFilters()
        orders = apply_filters_to_queryset(orders, filters)
        print(f"   After filters count: {orders.count()}")
        
        print("\n🔍 Step 3: Apply Limit")
        limit = 10  # Simulate a limit
        orders = orders[:limit]
        print(f"   After limit count: {orders.count()}")
        print(f"   QuerySet type: {type(orders)}")
        
        print("\n🔍 Step 4: Check if orders exist")
        exists = orders.exists()
        print(f"   Orders exist: {exists}")
        
        if not exists:
            print("❌ No orders found - this would cause empty file!")
            return False
        
        print("\n🔍 Step 5: Test iteration")
        order_list = []
        for i, order in enumerate(orders):
            order_list.append(order)
            print(f"   Order {i+1}: {order.customer_name} (ID: {order.id})")
            if i >= 4:  # Show first 5
                break
        
        print(f"   Total orders in iteration: {len(order_list)}")
        
        print("\n🔍 Step 6: Generate representative name")
        representative_name = generate_representative_name(user)
        print(f"   Representative: {representative_name}")
        
        print("\n🔍 Step 7: Export to Excel")
        file_path = export_orders_to_excel(
            orders=orders,
            representative_name=representative_name,
            export_date=date.today(),
            filename="api_test_export.xlsx",
        )
        
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✅ Export successful!")
            print(f"   File: {file_path}")
            print(f"   Size: {file_size:,} bytes")
            
            # Verify file content
            try:
                import openpyxl
                wb = openpyxl.load_workbook(file_path)
                ws = wb.active
                
                print(f"   Sheet: {ws.title}")
                print(f"   Dimensions: {ws.max_row} rows x {ws.max_column} columns")
                
                # Check for data
                data_rows = 0
                for row in range(7, min(12, ws.max_row + 1)):
                    row_data = []
                    has_data = False
                    for col in range(1, 9):
                        cell = ws.cell(row=row, column=col)
                        row_data.append(cell.value)
                        if cell.value is not None and str(cell.value).strip():
                            has_data = True
                    
                    if has_data:
                        data_rows += 1
                        print(f"   Data Row {row}: {row_data}")
                
                print(f"   Total data rows found: {data_rows}")
                
                if data_rows == 0:
                    print("❌ File has no data rows!")
                    
                    # Debug: Check what's in the orders QuerySet
                    print(f"\n🔍 QuerySet Debug:")
                    print(f"   Orders count: {orders.count()}")
                    print(f"   Orders evaluated: {list(orders.values_list('id', 'customer_name'))}")
                
                wb.close()
                
            except Exception as e:
                print(f"⚠️  Could not verify file: {e}")
            
            return data_rows > 0
        else:
            print(f"❌ Export failed - file not created")
            return False
            
    except Exception as e:
        print(f"❌ Error during API test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_queryset_slicing():
    """Test QuerySet slicing behavior."""
    print("\n=== Testing QuerySet Slicing ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        from accounts.models import User
        
        user = User.objects.first()
        
        print("🔍 Testing different QuerySet approaches:")
        
        # Test 1: Basic QuerySet
        orders1 = Order.objects.filter(office=user.office)
        print(f"   Basic QuerySet count: {orders1.count()}")
        
        # Test 2: With select_related
        orders2 = Order.objects.filter(office=user.office).select_related(
            "customer_company", "assigned_to", "order_delivery_status"
        )
        print(f"   With select_related count: {orders2.count()}")
        
        # Test 3: With slicing
        orders3 = orders2[:10]
        print(f"   With slicing count: {orders3.count()}")
        print(f"   Sliced QuerySet type: {type(orders3)}")
        
        # Test 4: Force evaluation
        orders4 = list(orders3)
        print(f"   Evaluated list length: {len(orders4)}")
        
        # Test 5: Test iteration on sliced QuerySet
        print(f"\n🔍 Testing iteration on sliced QuerySet:")
        count = 0
        for order in orders3:
            count += 1
            print(f"   Order {count}: {order.customer_name}")
            if count >= 3:
                break
        
        print(f"   Iteration count: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ QuerySet slicing test error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_empty_filters():
    """Test with empty filters to see if that's causing issues."""
    print("\n=== Testing Empty Filters ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        from orders.excel_export_schemas import apply_filters_to_queryset, OrderExportFilters
        from accounts.models import User
        
        user = User.objects.first()
        
        # Test with completely empty filters
        print("🔍 Testing with empty filters:")
        
        orders = Order.objects.filter(office=user.office)
        print(f"   Before filters: {orders.count()}")
        
        empty_filters = OrderExportFilters()
        filtered_orders = apply_filters_to_queryset(orders, empty_filters)
        print(f"   After empty filters: {filtered_orders.count()}")
        
        # Test with some filters
        print("🔍 Testing with status filter:")
        status_filters = OrderExportFilters(status=['PENDING', 'COMPLETED'])
        status_filtered = apply_filters_to_queryset(orders, status_filters)
        print(f"   After status filter: {status_filtered.count()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Empty filters test error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Starting API Export Debug\n")
    
    tests = [
        ("QuerySet Slicing", test_queryset_slicing),
        ("Empty Filters", test_empty_filters),
        ("API Export Logic", test_api_export),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'='*60}")
    
    # Summary
    print(f"\n🏁 Test Results Summary:")
    print(f"{'='*60}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed!")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
