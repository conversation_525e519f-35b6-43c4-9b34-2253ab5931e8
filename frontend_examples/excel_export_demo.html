<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel Export Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        
        .section h2 {
            color: #555;
            margin-top: 0;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .preview-button {
            background-color: #28a745;
        }
        
        .preview-button:hover {
            background-color: #218838;
        }
        
        .results {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .order-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .order-item {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
        }
        
        .order-item input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        @media (max-width: 768px) {
            .filter-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Excel Export Demo</h1>
        
        <!-- Selective Export Section -->
        <div class="section">
            <h2>🎯 Selective Export</h2>
            <p>Select specific orders to export:</p>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="selectAll"> Select All Orders
                </label>
            </div>
            
            <div class="order-list" id="orderList">
                <!-- Orders will be populated here -->
            </div>
            
            <div class="form-group">
                <label for="selectiveFilename">Custom Filename (optional):</label>
                <input type="text" id="selectiveFilename" placeholder="my_orders.xlsx">
            </div>
            
            <button onclick="exportSelected()">📥 Export Selected Orders</button>
            <div id="selectiveResults" class="results"></div>
        </div>
        
        <!-- Bulk Export Section -->
        <div class="section">
            <h2>📦 Bulk Export with Filters</h2>
            
            <div class="filter-row">
                <div class="form-group">
                    <label for="startDate">Start Date:</label>
                    <input type="date" id="startDate">
                </div>
                <div class="form-group">
                    <label for="endDate">End Date:</label>
                    <input type="date" id="endDate">
                </div>
            </div>
            
            <div class="filter-row">
                <div class="form-group">
                    <label for="statusFilter">Order Status:</label>
                    <select id="statusFilter" multiple>
                        <option value="pending">Pending</option>
                        <option value="confirmed">Confirmed</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="companyCodes">Company Codes (comma-separated):</label>
                    <input type="text" id="companyCodes" placeholder="ABC, XYZ, DEF">
                </div>
            </div>
            
            <div class="filter-row">
                <div class="form-group">
                    <label for="minPrice">Minimum Price:</label>
                    <input type="number" id="minPrice" step="0.01" placeholder="0.00">
                </div>
                <div class="form-group">
                    <label for="maxPrice">Maximum Price:</label>
                    <input type="number" id="maxPrice" step="0.01" placeholder="1000.00">
                </div>
            </div>
            
            <div class="filter-row">
                <div class="form-group">
                    <label for="customerName">Customer Name Contains:</label>
                    <input type="text" id="customerName" placeholder="أحمد">
                </div>
                <div class="form-group">
                    <label for="exportLimit">Export Limit:</label>
                    <input type="number" id="exportLimit" placeholder="1000" value="1000">
                </div>
            </div>
            
            <div class="form-group">
                <label for="bulkFilename">Custom Filename (optional):</label>
                <input type="text" id="bulkFilename" placeholder="bulk_export.xlsx">
            </div>
            
            <button class="preview-button" onclick="previewExport()">👁️ Preview Export</button>
            <button onclick="exportBulk()">📥 Export with Filters</button>
            
            <div id="bulkResults" class="results"></div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE_URL = '/api';
        const AUTH_TOKEN = 'your-auth-token-here'; // Replace with actual token
        
        // Sample orders for demo
        const sampleOrders = [
            { id: 1, code: 'ORD-001', customer_name: 'أحمد محمد', total_price: 250.0, status: 'completed' },
            { id: 2, code: 'ORD-002', customer_name: 'فاطمة علي', total_price: 180.0, status: 'pending' },
            { id: 3, code: 'ORD-003', customer_name: 'محمد حسن', total_price: 320.0, status: 'in_progress' },
            { id: 4, code: 'ORD-004', customer_name: 'سارة أحمد', total_price: 150.0, status: 'completed' },
            { id: 5, code: 'ORD-005', customer_name: 'علي محمود', total_price: 400.0, status: 'confirmed' }
        ];
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            populateOrderList();
            setupEventListeners();
            setDefaultDates();
        });
        
        function populateOrderList() {
            const orderList = document.getElementById('orderList');
            orderList.innerHTML = '';
            
            sampleOrders.forEach(order => {
                const orderItem = document.createElement('div');
                orderItem.className = 'order-item';
                orderItem.innerHTML = `
                    <label>
                        <input type="checkbox" value="${order.id}" class="order-checkbox">
                        <strong>${order.code}</strong><br>
                        ${order.customer_name}<br>
                        $${order.total_price} - ${order.status}
                    </label>
                `;
                orderList.appendChild(orderItem);
            });
        }
        
        function setupEventListeners() {
            // Select all checkbox
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.order-checkbox');
                checkboxes.forEach(cb => cb.checked = this.checked);
            });
        }
        
        function setDefaultDates() {
            const today = new Date();
            const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
            
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            document.getElementById('startDate').value = thirtyDaysAgo.toISOString().split('T')[0];
        }
        
        function getSelectedOrderIds() {
            const checkboxes = document.querySelectorAll('.order-checkbox:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }
        
        function getBulkFilters() {
            const filters = {};
            
            // Date range
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            if (startDate || endDate) {
                filters.created_date_range = {};
                if (startDate) filters.created_date_range.start_date = startDate;
                if (endDate) filters.created_date_range.end_date = endDate;
            }
            
            // Status
            const statusSelect = document.getElementById('statusFilter');
            const selectedStatuses = Array.from(statusSelect.selectedOptions).map(option => option.value);
            if (selectedStatuses.length > 0) {
                filters.status = selectedStatuses;
            }
            
            // Company codes
            const companyCodes = document.getElementById('companyCodes').value;
            if (companyCodes.trim()) {
                filters.company_codes = companyCodes.split(',').map(code => code.trim());
            }
            
            // Price range
            const minPrice = document.getElementById('minPrice').value;
            const maxPrice = document.getElementById('maxPrice').value;
            if (minPrice) filters.min_total_price = parseFloat(minPrice);
            if (maxPrice) filters.max_total_price = parseFloat(maxPrice);
            
            // Customer name
            const customerName = document.getElementById('customerName').value;
            if (customerName.trim()) {
                filters.customer_name_contains = customerName.trim();
            }
            
            return filters;
        }
        
        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `results ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
            
            // Auto-hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    element.style.display = 'none';
                }, 5000);
            }
        }
        
        async function exportSelected() {
            const orderIds = getSelectedOrderIds();
            
            if (orderIds.length === 0) {
                showResult('selectiveResults', 'error', '❌ Please select at least one order to export.');
                return;
            }
            
            const filename = document.getElementById('selectiveFilename').value || null;
            
            const requestData = {
                export_type: 'selective',
                order_ids: orderIds,
                filename: filename
            };
            
            try {
                showResult('selectiveResults', 'info', '⏳ Exporting selected orders...');
                
                const response = await fetch(`${API_BASE_URL}/orders/export-excel/selective`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${AUTH_TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename || `selected_orders_${orderIds.length}_items.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    
                    showResult('selectiveResults', 'success', 
                        `✅ Successfully exported ${orderIds.length} orders!`);
                } else {
                    const error = await response.json();
                    showResult('selectiveResults', 'error', 
                        `❌ Export failed: ${error.message || 'Unknown error'}`);
                }
            } catch (error) {
                showResult('selectiveResults', 'error', 
                    `❌ Network error: ${error.message}`);
            }
        }
        
        async function previewExport() {
            const filters = getBulkFilters();
            const limit = document.getElementById('exportLimit').value;
            
            const requestData = {
                export_type: 'bulk',
                filters: filters,
                limit: limit ? parseInt(limit) : null
            };
            
            try {
                showResult('bulkResults', 'info', '⏳ Generating preview...');
                
                const response = await fetch(`${API_BASE_URL}/orders/export-excel/preview`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${AUTH_TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    
                    let previewHtml = `
                        <h3>📋 Export Preview</h3>
                        <p><strong>Total Orders:</strong> ${result.total_orders}</p>
                        <p><strong>Estimated File Size:</strong> ${result.estimated_file_size_mb} MB</p>
                    `;
                    
                    if (result.sample_orders && result.sample_orders.length > 0) {
                        previewHtml += '<h4>Sample Orders:</h4><ul>';
                        result.sample_orders.forEach(order => {
                            previewHtml += `
                                <li>${order.code} - ${order.customer_name} - $${order.total_price}</li>
                            `;
                        });
                        previewHtml += '</ul>';
                    }
                    
                    showResult('bulkResults', 'info', previewHtml);
                } else {
                    const error = await response.json();
                    showResult('bulkResults', 'error', 
                        `❌ Preview failed: ${error.message || 'Unknown error'}`);
                }
            } catch (error) {
                showResult('bulkResults', 'error', 
                    `❌ Network error: ${error.message}`);
            }
        }
        
        async function exportBulk() {
            const filters = getBulkFilters();
            const limit = document.getElementById('exportLimit').value;
            const filename = document.getElementById('bulkFilename').value || null;
            
            const requestData = {
                export_type: 'bulk',
                filters: filters,
                filename: filename,
                limit: limit ? parseInt(limit) : null
            };
            
            try {
                showResult('bulkResults', 'info', '⏳ Exporting orders with filters...');
                
                const response = await fetch(`${API_BASE_URL}/orders/export-excel/bulk`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${AUTH_TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename || 'bulk_export.xlsx';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    
                    showResult('bulkResults', 'success', 
                        '✅ Bulk export completed successfully!');
                } else {
                    const error = await response.json();
                    showResult('bulkResults', 'error', 
                        `❌ Export failed: ${error.message || 'Unknown error'}`);
                }
            } catch (error) {
                showResult('bulkResults', 'error', 
                    `❌ Network error: ${error.message}`);
            }
        }
    </script>
</body>
</html>
