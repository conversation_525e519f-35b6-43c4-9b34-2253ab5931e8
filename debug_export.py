#!/usr/bin/env python3
"""
Debug script to test Excel export functionality and identify why files are empty.
"""

import sys
import os
from datetime import date

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_export():
    """Debug the Excel export functionality."""
    print("=== Debugging Excel Export ===")
    
    try:
        # Import Django and setup
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        from orders.excel_export_service import export_orders_to_excel, generate_representative_name
        from accounts.models import User
        
        # Get some test orders
        orders = Order.objects.all()[:3]  # Get first 3 orders
        
        print(f"📊 Found {orders.count()} orders for testing")
        
        if not orders.exists():
            print("❌ No orders found in database")
            return False
        
        # Debug: Check order data
        print("\n🔍 Order Details:")
        for i, order in enumerate(orders, 1):
            print(f"  Order {i}:")
            print(f"    ID: {order.id}")
            print(f"    Code: {order.code}")
            print(f"    Customer: {order.customer_name}")
            print(f"    Phone: {order.customer_phone}")
            print(f"    Company: {order.customer_company.name if order.customer_company else 'None'}")
            print(f"    Price: {order.total_price}")
            print(f"    Status: {order.order_handling_status}")
            print(f"    Notes: {order.notes}")
            print()
        
        # Test representative name generation
        user = User.objects.first()
        if user:
            rep_name = generate_representative_name(user)
            print(f"✅ Representative name: '{rep_name}'")
        else:
            rep_name = "Test Office - Test User"
            print(f"⚠️  No users found, using default: '{rep_name}'")
        
        # Test export
        print(f"\n📁 Exporting {orders.count()} orders...")
        
        file_path = export_orders_to_excel(
            orders=orders,
            representative_name=rep_name,
            export_date=date.today(),
            filename="debug_export.xlsx"
        )
        
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✅ Export successful!")
            print(f"   File: {file_path}")
            print(f"   Size: {file_size:,} bytes")
            
            # Verify file structure
            try:
                import openpyxl
                wb = openpyxl.load_workbook(file_path)
                ws = wb.active
                
                print(f"   Sheet: {ws.title}")
                print(f"   Dimensions: {ws.max_row} rows x {ws.max_column} columns")
                
                # Check key cells
                rep_cell = ws['B3'].value
                date_cell = ws['B5'].value
                
                print(f"   Representative (B3): '{rep_cell}'")
                print(f"   Date (B5): '{date_cell}'")
                
                # Check headers
                print("   Headers (Row 6):")
                for col in range(1, 9):
                    cell = ws.cell(row=6, column=col)
                    print(f"     Column {col}: '{cell.value}'")
                
                # Check data rows
                print("   Data rows:")
                data_found = False
                for row in range(7, min(12, ws.max_row + 1)):
                    row_data = []
                    has_data = False
                    for col in range(1, 9):
                        cell = ws.cell(row=row, column=col)
                        row_data.append(cell.value)
                        if cell.value is not None and str(cell.value).strip():
                            has_data = True
                    
                    if has_data:
                        data_found = True
                        print(f"     Row {row}: {row_data}")
                
                if not data_found:
                    print("     ❌ No data found in rows 7+")
                    
                    # Check if orders QuerySet is actually populated
                    print(f"\n🔍 QuerySet Debug:")
                    print(f"   Orders count: {orders.count()}")
                    print(f"   Orders list: {list(orders.values_list('id', 'customer_name'))}")
                
                wb.close()
                
            except Exception as e:
                print(f"⚠️  Could not verify file structure: {e}")
                import traceback
                traceback.print_exc()
            
            return True
        else:
            print(f"❌ Export failed - file not created")
            return False
            
    except Exception as e:
        print(f"❌ Error during export test: {e}")
        import traceback
        traceback.print_exc()
        return False


def debug_queryset():
    """Debug the QuerySet to see if it's properly populated."""
    print("\n=== Debugging QuerySet ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        
        # Test different QuerySet approaches
        print("🔍 Testing QuerySet approaches:")
        
        # Basic QuerySet
        orders1 = Order.objects.all()[:3]
        print(f"   Basic QuerySet: {orders1.count()} orders")
        
        # With select_related
        orders2 = Order.objects.select_related('customer_company', 'assigned_to', 'order_delivery_status')[:3]
        print(f"   With select_related: {orders2.count()} orders")
        
        # Test iteration
        print(f"\n🔍 Testing iteration:")
        for i, order in enumerate(orders2, 1):
            print(f"   Order {i}: {order.customer_name} (ID: {order.id})")
            if i >= 3:
                break
        
        # Test if QuerySet is lazy-loaded
        print(f"\n🔍 Testing QuerySet evaluation:")
        orders3 = Order.objects.all()[:3]
        print(f"   Before evaluation: {type(orders3)}")
        order_list = list(orders3)
        print(f"   After evaluation: {len(order_list)} orders")
        
        return True
        
    except Exception as e:
        print(f"❌ QuerySet debug error: {e}")
        import traceback
        traceback.print_exc()
        return False


def debug_template():
    """Debug template loading."""
    print("\n=== Debugging Template ===")
    
    try:
        import openpyxl
        
        template_path = 'نسخة مناديب اورجينال - Copy.xlsx'
        
        if not os.path.exists(template_path):
            print(f"❌ Template file not found: {template_path}")
            return False
        
        print(f"✅ Template file found: {template_path}")
        
        wb = openpyxl.load_workbook(template_path)
        ws = wb.active
        
        print(f"   Sheet: {ws.title}")
        print(f"   Dimensions: {ws.max_row} rows x {ws.max_column} columns")
        
        # Check if template has data
        print("   Template data check:")
        for row in range(7, min(12, ws.max_row + 1)):
            row_data = []
            has_data = False
            for col in range(1, 9):
                cell = ws.cell(row=row, column=col)
                row_data.append(cell.value)
                if cell.value is not None:
                    has_data = True
            
            if has_data:
                print(f"     Row {row}: {row_data}")
        
        wb.close()
        return True
        
    except Exception as e:
        print(f"❌ Template debug error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all debug tests."""
    print("🚀 Starting Excel Export Debug\n")
    
    tests = [
        ("Template Debug", debug_template),
        ("QuerySet Debug", debug_queryset),
        ("Export Debug", debug_export),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'='*60}")
    
    # Summary
    print(f"\n🏁 Debug Results Summary:")
    print(f"{'='*60}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All debug tests passed!")
        return True
    else:
        print("⚠️  Some debug tests failed. Check the output above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
