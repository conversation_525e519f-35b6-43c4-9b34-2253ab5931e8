#!/usr/bin/env python3
"""
Test script for intelligent column mapping functionality.
Tests the new flexible Excel import with automatic column detection.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_intelligent_column_mapping():
    """Test intelligent column mapping with the new Excel file."""
    print("=== Testing Intelligent Column Mapping ===")
    
    try:
        from orders.intelligent_column_mapper import IntelligentColumnMapper, create_intelligent_mapping
        
        # Test with the new Excel file format
        file_path = "السبت (1).xlsx"
        
        if not os.path.exists(file_path):
            print(f"❌ Test file not found: {file_path}")
            return False
        
        print(f"📁 Analyzing file: {file_path}")
        
        # Create intelligent mapping
        column_mapping, data_start_row, validation_result = create_intelligent_mapping(file_path)
        
        print(f"📊 Intelligent Mapping Results:")
        print(f"   Column mapping: {column_mapping}")
        print(f"   Data starts at row: {data_start_row}")
        print(f"   Validation valid: {validation_result['valid']}")
        print(f"   Mapped fields: {validation_result['mapped_fields']}")
        
        if validation_result['missing_required']:
            print(f"   Missing required: {validation_result['missing_required']}")
        
        if validation_result['warnings']:
            print(f"   Warnings: {validation_result['warnings']}")
        
        # Show mapping summary
        mapper = IntelligentColumnMapper()
        import pandas as pd
        df = pd.read_excel(file_path, header=None)
        
        # Find header row
        header_row = mapper.detect_header_row(df)
        if header_row is not None:
            summary = mapper.get_mapping_summary(column_mapping, df, header_row)
            print(f"\n📋 Mapping Summary:")
            print(summary)
        
        return validation_result['valid']
        
    except Exception as e:
        print(f"❌ Error during intelligent mapping test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_intelligent_parsing():
    """Test intelligent parsing with the new Excel file."""
    print("\n=== Testing Intelligent Parsing ===")
    
    try:
        from orders.excel_parser_service import parse_excel_orders_intelligent
        from orders.excel_import_schemas import ExcelImportConfig
        
        file_path = "السبت (1).xlsx"
        
        if not os.path.exists(file_path):
            print(f"❌ Test file not found: {file_path}")
            return False
        
        print(f"📁 Parsing file with intelligent mapping: {file_path}")
        
        # Use minimal config since intelligent mapping will handle column detection
        config = ExcelImportConfig(
            batch_size=50,
            max_errors=10,
            skip_empty_rows=True,
            require_customer_name=True,
            require_customer_phone=True,
            require_company_code=False
        )
        
        # Parse the Excel file
        import_result, processed_orders = parse_excel_orders_intelligent(file_path, config)
        
        print(f"📊 Intelligent Parsing Results:")
        print(f"   Total rows processed: {import_result.total_rows_processed}")
        print(f"   Successful imports: {import_result.successful_imports}")
        print(f"   Failed imports: {import_result.failed_imports}")
        print(f"   Skipped rows: {import_result.skipped_rows}")
        print(f"   Success rate: {import_result.success_rate:.1f}%")
        
        if import_result.errors:
            print(f"\n⚠️  Errors ({len(import_result.errors)}):")
            for error in import_result.errors[:5]:  # Show first 5 errors
                print(f"   Row {error['row_number']}: {error['error_message']}")
            if len(import_result.errors) > 5:
                print(f"   ... and {len(import_result.errors) - 5} more errors")
        
        if import_result.warnings:
            print(f"\n⚠️  Warnings ({len(import_result.warnings)}):")
            for warning in import_result.warnings[:3]:  # Show first 3 warnings
                print(f"   Row {warning['row_number']}: {warning['warning_message']}")
        
        # Show sample processed orders
        if processed_orders:
            print(f"\n✅ Sample processed orders ({len(processed_orders)} total):")
            for i, order in enumerate(processed_orders[:5]):
                print(f"   Order {i+1}:")
                print(f"     Order Code: {order.order_code} (auto-generated)")
                print(f"     Customer: {order.customer_name}")
                print(f"     Phone: {order.customer_phone}")
                print(f"     Price: {order.total_price}")
                print(f"     Company Code: {order.company_code}")
                print(f"     Company Name: {order.customer_company_name}")
                print(f"     Address: {order.customer_address}")
                print()
        
        return len(processed_orders) > 0
        
    except Exception as e:
        print(f"❌ Error during intelligent parsing test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_comparison_with_original():
    """Compare intelligent mapping with the original file."""
    print("\n=== Testing Comparison with Original File ===")
    
    try:
        from orders.excel_parser_service import parse_excel_orders_intelligent
        from orders.excel_import_schemas import ExcelImportConfig
        
        # Test both files
        files = [
            "8-2-2025 رين واى.xlsx تمانيات.xlsx",  # Original file
            "السبت (1).xlsx"  # New file
        ]
        
        config = ExcelImportConfig(
            batch_size=50,
            max_errors=10,
            skip_empty_rows=True,
            require_customer_name=True,
            require_customer_phone=True,
            require_company_code=False
        )
        
        results = {}
        
        for file_path in files:
            if not os.path.exists(file_path):
                print(f"⚠️  File not found: {file_path}")
                continue
                
            print(f"\n📁 Testing file: {file_path}")
            
            try:
                import_result, processed_orders = parse_excel_orders_intelligent(file_path, config)
                
                results[file_path] = {
                    'total_rows': import_result.total_rows_processed,
                    'successful': import_result.successful_imports,
                    'failed': import_result.failed_imports,
                    'success_rate': import_result.success_rate,
                    'orders': len(processed_orders)
                }
                
                print(f"   ✅ Success: {import_result.successful_imports}/{import_result.total_rows_processed} ({import_result.success_rate:.1f}%)")
                
            except Exception as e:
                print(f"   ❌ Failed: {e}")
                results[file_path] = {'error': str(e)}
        
        # Compare results
        print(f"\n📊 Comparison Summary:")
        print(f"{'File':<40} {'Success Rate':<15} {'Orders':<10}")
        print("=" * 70)
        
        for file_path, result in results.items():
            if 'error' in result:
                print(f"{file_path:<40} {'ERROR':<15} {'N/A':<10}")
            else:
                print(f"{file_path:<40} {result['success_rate']:.1f}%{'':<10} {result['orders']:<10}")
        
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ Error during comparison test: {e}")
        return False


def main():
    """Run all intelligent mapping tests."""
    print("🚀 Starting Intelligent Column Mapping Tests\n")
    
    tests = [
        ("Intelligent Column Mapping", test_intelligent_column_mapping),
        ("Intelligent Parsing", test_intelligent_parsing),
        ("Comparison with Original", test_comparison_with_original),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'='*60}")
    
    # Summary
    print(f"\n🏁 Test Results Summary:")
    print(f"{'='*60}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! The intelligent column mapping is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
