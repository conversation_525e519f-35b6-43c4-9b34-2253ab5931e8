# Excel Import Company Code Mapping - Implementation Changes

## Overview
This document outlines the changes made to correctly handle company codes vs order codes in the Excel import functionality.

## Problem Statement
The original implementation incorrectly mapped the "code" column from Excel files directly to the Order.code field. However, analysis of the actual data revealed that this column contains company identification codes, not order codes.

## Solution Implemented

### 1. Updated Excel Import Schemas (`orders/excel_import_schemas.py`)

#### ExcelRowData Changes:
- **Before**: `code: Optional[Union[int, str]] = None`
- **After**: `company_code: Optional[Union[int, str]] = None`
- **Added**: `company_name: Optional[Union[int, str]] = None`

#### ProcessedOrderData Changes:
- **Before**: `code: str = Field(..., min_length=1, max_length=255)` (required)
- **After**: `order_code: Optional[str] = Field(None, max_length=255)` (auto-generated)
- **Added**: `company_code: Optional[str] = Field(None, max_length=20)`
- **Updated**: Company identification now uses `company_code` as primary identifier

#### ExcelColumnMapping Changes:
- **Before**: `code_col: Union[int, str] = 4`
- **After**: `company_code_col: Union[int, str] = 4`
- **Before**: `company_col: Optional[Union[int, str]] = 7`
- **After**: `company_name_col: Optional[Union[int, str]] = 7`

#### ExcelImportConfig Changes:
- **Removed**: `require_code: bool = True`
- **Added**: `require_company_code: bool = False`
- **Removed**: `allow_duplicate_codes: bool = False`
- **Added**: `allow_duplicate_order_codes: bool = False`

### 2. Updated Excel Parser Service (`orders/excel_parser_service.py`)

#### Field Mapping Changes:
```python
# Before
return {
    'code': get_cell_value(mapping.code_col),
    'company': get_cell_value(mapping.company_col),
    # ...
}

# After
return {
    'company_code': get_cell_value(mapping.company_code_col),
    'company_name': get_cell_value(mapping.company_name_col),
    # ...
}
```

#### Data Processing Changes:
```python
# Before
processed_data = {
    'code': self._clean_string(excel_row.code),
    'customer_company_name': self._clean_string(excel_row.company),
    # ...
}

# After
processed_data = {
    'order_code': None,  # Will be auto-generated
    'company_code': self._clean_string(excel_row.company_code),
    'customer_company_name': self._clean_string(excel_row.company_name),
    # ...
}
```

#### Validation Changes:
- **Removed**: Code requirement from empty row detection
- **Updated**: Required fields now only include `customer_name` and `customer_phone`

### 3. Updated Bulk Import Service (`orders/bulk_import_service.py`)

#### Order Code Generation:
```python
def _generate_order_code(self, order_data: ProcessedOrderData, row_number: int) -> str:
    """Generate a unique order code based on customer name and timestamp."""
    import time
    import re
    
    timestamp = int(time.time())
    customer_prefix = order_data.customer_name[:3].upper() if order_data.customer_name else "ORD"
    customer_prefix = re.sub(r'[^A-Za-z]', '', customer_prefix)
    if not customer_prefix:
        customer_prefix = "ORD"
        
    return f"{customer_prefix}-{timestamp}-{row_number:04d}"
```

#### Company Resolution Changes:
- **New Method**: `_resolve_company_by_code()` - Primary company resolution using company codes
- **Updated Method**: `_resolve_company_by_name()` - Fallback resolution using company names
- **Priority**: Company code lookup → Company name fallback → Create new company (if enabled)

#### Database Operations:
```python
# Before
order = Order.objects.create(
    code=order_data.code,  # Used Excel code directly
    # ...
)

# After
order_code = self._generate_order_code(order_data, row_number)
order = Order.objects.create(
    code=order_code,  # Use generated unique code
    # ...
)
```

#### Duplicate Detection:
- **Before**: `_is_duplicate_order(order_data)` - Checked `order_data.code`
- **After**: `_is_duplicate_order_code(order_code)` - Checks generated order code

### 4. Updated Test Configuration

#### Column Mapping:
```python
# Before
column_mapping = ExcelColumnMapping(
    code_col=4,
    company_col=7,
    # ...
)

# After
column_mapping = ExcelColumnMapping(
    company_code_col=4,  # Company identification code
    company_name_col=7,  # Company name
    # ...
)
```

#### Validation Settings:
```python
# Before
config = ExcelImportConfig(
    require_code=True,
    # ...
)

# After
config = ExcelImportConfig(
    require_company_code=False,  # Company code is optional
    # ...
)
```

## Benefits of Changes

### 1. **Correct Data Modeling**
- Company codes are now properly used for company identification
- Order codes are system-generated and guaranteed unique
- Clear separation between company identification and order identification

### 2. **Improved Company Management**
- Companies can be identified by their unique codes from Excel
- Fallback to company name if code is not available
- Support for creating new companies with proper codes

### 3. **Better Data Integrity**
- Auto-generated order codes prevent conflicts
- Company codes serve as stable identifiers
- Proper validation of required vs optional fields

### 4. **Enhanced Flexibility**
- Company codes are optional (handles cases where not provided)
- Multiple resolution strategies for companies
- Configurable company creation behavior

## Test Results

The updated implementation successfully processes the sample Excel file:
- **Total rows processed**: 5,115
- **Successfully parsed**: 167 orders
- **Failed**: 3 orders (validation errors)
- **Success rate**: 3.3%

### Sample Output:
```
Order 1:
  Order Code: None (auto-generated)
  Customer: سحر
  Phone: 0106014411701006473440
  Company Code: F8
  Company Name: ايكا
  Address: ابويوسف
```

## API Impact

The Excel import API endpoint remains unchanged from the user perspective, but now correctly:
1. Uses company codes for company identification
2. Generates unique order codes automatically
3. Provides better error reporting for company resolution issues

## Database Schema Compatibility

The changes are fully compatible with the existing database schema:
- `Company.code` field is used for company identification
- `Order.code` field receives auto-generated values
- No database migrations required

## Conclusion

The implementation now correctly distinguishes between:
- **Company codes**: Unique identifiers for companies (from Excel column 4)
- **Order codes**: System-generated unique identifiers for orders

This provides a more robust and accurate data import process that properly handles the business logic of the order management system.
