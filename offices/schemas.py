from ninja import Schema
from ninja_schema import ModelSchema
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from offices.models import Office


class OfficeEditSchema(Schema):
    name: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = None


class OfficeOutSchema(ModelSchema):
    class Config:
        model = Office
        depth = 1
        include = "__all__"


class DateRangeSchema(Schema):
    start_date: date
    end_date: date


class DailyPerformanceSchema(Schema):
    date: date
    total_orders: int
    completed_orders: int
    revenue: float


class EmployeePerformanceSchema(Schema):
    employee_id: int
    employee_name: str
    total_orders_assigned: int
    total_orders_completed: int
    completion_rate: float
    average_completion_time: Optional[float] = None  # in hours
    total_revenue: float
    commission_earned: float
    average_order_value: float
    on_time_delivery_percentage: float
    orders_by_status: Dict[str, int]
    daily_performance: List[DailyPerformanceSchema]


class OfficePerformanceSchema(Schema):
    total_orders: int
    orders_by_status: Dict[str, int]
    orders_by_delivery_status: Dict[str, int]
    total_revenue: float
    average_order_value: float
    collection_rate: float
    average_completion_time: Optional[float] = None  # in hours
    on_time_delivery_rate: float
    orders_by_company: List[Dict[str, Any]]
    top_performing_employees: List[Dict[str, Any]]
    daily_performance: List[Dict[str, Any]]


class HistoricalActivitySchema(Schema):
    id: str  # Composite ID like "assignment_1" or "status_1"
    timestamp: datetime
    activity_type: str  # "ORDER_ASSIGNMENT", "STATUS_CHANGE"
    user_name: str
    user_id: int
    description: str  # Arabic description of the activity
    order_id: Optional[int] = None
    order_code: Optional[str] = None
    customer_name: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class HistoricalActivityResponseSchema(Schema):
    activities: List[HistoricalActivitySchema]
    total_count: int
    page: int
    page_size: int
    has_more: bool
