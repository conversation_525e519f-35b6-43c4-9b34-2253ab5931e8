from django.db import models
from django.utils.translation import gettext_lazy as _


class Office(models.Model):
    """
    Represents a tenant (delivery office) in the system.
    Each office is isolated and has its own managers and employees.
    """

    name = models.CharField(max_length=255)
    address = models.TextField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = "offices"
        verbose_name = _("Office")
        verbose_name_plural = _("Offices")
