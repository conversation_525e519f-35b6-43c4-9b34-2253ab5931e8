from ninja import Router
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.db.models import Count, Sum, Q
from datetime import timedel<PERSON>, datetime
from typing import List
import asyncio
import json
from asgiref.sync import sync_to_async
from django.db import connection
from django.core.cache import cache
from django.utils import timezone

from offices.models import Office
from .schemas import (
    OfficeEditSchema,
    OfficeOutSchema,
    DateRangeSchema,
    EmployeePerformanceSchema,
    OfficePerformanceSchema,
    HistoricalActivitySchema,
    HistoricalActivityResponseSchema,
)
from core.middleware import AuthBearer
from core.errors import HttpError
from accounts.models import Role, User
from accounts.schemas import UserModelSchema
from orders.models import (
    Order,
    OrderHandlingStatus,
    OrderAssignmentHistory,
    OrderHandlingStatusHistory,
)

router = Router()


@router.put("/offices/{office_id}/", response=OfficeOutSchema, auth=AuthBearer())
def edit_office(request, office_id: int, payload: OfficeEditSchema):
    user = request.auth
    if user.role != Role.MASTER:
        raise HttpError(403, "Forbidden: Only Master Users can edit offices.")

    with transaction.atomic():
        office = get_object_or_404(Office, id=office_id)
        for attr, value in payload.dict(exclude_unset=True).items():
            setattr(office, attr, value)
        office.save()
    return office


@router.get("/offices/users", response=List[UserModelSchema], auth=AuthBearer())
def list_office_users(request):
    """List all users who belong to the same office."""
    user = request.auth
    # Verify the office exists
    office: Office = get_object_or_404(Office, id=user.office_id)

    # Get all users in the office
    users = User.objects.filter(office_id=office.id, is_active=True)
    return list(users)


@router.post(
    "/offices/performance", response=OfficePerformanceSchema, auth=AuthBearer()
)
def get_office_performance(request, date_range: DateRangeSchema):
    """Get performance report for the entire office."""
    user = request.auth

    # Only managers and master users can access office performance reports
    if user.role not in [Role.MANAGER, Role.MASTER]:
        raise HttpError(
            403,
            "Forbidden: Only Managers and Master Users can access office performance reports.",
        )

    # Get the office
    office = get_object_or_404(Office, id=user.office_id)

    # Filter orders by date range and office
    # Convert date objects to timezone-aware datetime objects
    start_date = timezone.make_aware(
        datetime.combine(date_range.start_date, datetime.min.time())
    )
    end_date = timezone.make_aware(
        datetime.combine(date_range.end_date, datetime.max.time())
    )

    orders = Order.objects.filter(
        office=office,
        created_at__gte=start_date,
        created_at__lte=end_date,  # end_date already includes the full day
    )

    # Calculate total orders
    total_orders = orders.count()

    # Calculate orders by status
    orders_by_status = dict(
        orders.values("order_handling_status")
        .annotate(count=Count("id"))
        .values_list("order_handling_status", "count")
    )

    # Calculate orders by delivery status
    delivery_status_data = (
        orders.values("order_delivery_status__name")
        .annotate(count=Count("id"))
        .values_list("order_delivery_status__name", "count")
    )

    orders_by_delivery_status = {}
    for status_name, count in delivery_status_data:
        # Handle None values by providing a default label
        key = status_name if status_name is not None else "No Status"
        orders_by_delivery_status[key] = count

    # Calculate financial metrics
    completed_orders = orders.filter(
        order_handling_status=OrderHandlingStatus.COMPLETED
    )
    total_revenue = (
        completed_orders.aggregate(Sum("total_price"))["total_price__sum"] or 0
    )
    average_order_value = (
        total_revenue / completed_orders.count() if completed_orders.count() > 0 else 0
    )

    # Calculate collection rate (amount collected vs total order value)
    total_collected = (
        completed_orders.aggregate(Sum("delivery_customer_payment"))[
            "delivery_customer_payment__sum"
        ]
        or 0
    )
    collection_rate = (
        (total_collected / total_revenue) * 100 if total_revenue > 0 else 0
    )

    # Calculate efficiency metrics
    # For average completion time, we need to calculate the time between order creation and completion
    # This is a simplified version - in a real system, you might want to use the status history
    avg_completion_time = None
    on_time_delivery_count = 0

    for order in completed_orders:
        if order.assigned_at and order.updated_at:
            # If we have both timestamps, we can calculate completion time
            completion_time = (
                order.updated_at - order.assigned_at
            ).total_seconds() / 3600  # in hours
            if avg_completion_time is None:
                avg_completion_time = completion_time
            else:
                avg_completion_time = (avg_completion_time + completion_time) / 2

        # Check if order was delivered on time
        if (
            order.delivery_deadline_date
            and order.updated_at <= order.delivery_deadline_date
        ):
            on_time_delivery_count += 1

    on_time_delivery_rate = (
        (on_time_delivery_count / completed_orders.count()) * 100
        if completed_orders.count() > 0
        else 0
    )

    # Calculate orders by company
    orders_by_company = list(
        orders.values("customer_company__name")
        .annotate(count=Count("id"), revenue=Sum("total_price"))
        .order_by("-count")
    )

    # Get top performing employees
    top_employees = (
        User.objects.filter(office=office, role=Role.EMPLOYEE, order__in=orders)
        .annotate(
            completed_orders=Count(
                "order",
                filter=Q(order__order_handling_status=OrderHandlingStatus.COMPLETED),
            ),
            total_revenue=Sum(
                "order__total_price",
                filter=Q(order__order_handling_status=OrderHandlingStatus.COMPLETED),
            ),
        )
        .order_by("-completed_orders")[:5]
    )

    top_performing_employees = []
    for emp in top_employees:
        top_performing_employees.append(
            {
                "employee_id": int(emp.id),
                "employee_name": emp.get_full_name(),
                "completed_orders": float(emp.completed_orders)
                if emp.completed_orders
                else 0,
                "total_revenue": float(emp.total_revenue) if emp.total_revenue else 0,
            }
        )

    # Calculate daily performance
    daily_performance = []
    current_date = start_date.date() if hasattr(start_date, "date") else start_date
    end_date_only = end_date.date() if hasattr(end_date, "date") else end_date

    while current_date <= end_date_only:
        next_date = current_date + timedelta(days=1)
        # Convert to timezone-aware datetime for filtering
        current_datetime = timezone.make_aware(
            datetime.combine(current_date, datetime.min.time())
        )
        next_datetime = timezone.make_aware(
            datetime.combine(next_date, datetime.min.time())
        )

        daily_orders = orders.filter(
            created_at__gte=current_datetime, created_at__lt=next_datetime
        )
        daily_completed = daily_orders.filter(
            order_handling_status=OrderHandlingStatus.COMPLETED
        )

        daily_performance.append(
            {
                "date": current_date.isoformat(),
                "total_orders": daily_orders.count(),
                "completed_orders": daily_completed.count(),
                "revenue": float(
                    daily_completed.aggregate(Sum("total_price"))["total_price__sum"]
                    or 0
                ),
            }
        )

        current_date = next_date

    # Prepare the response
    return {
        "total_orders": total_orders,
        "orders_by_status": orders_by_status,
        "orders_by_delivery_status": orders_by_delivery_status,
        "total_revenue": total_revenue,
        "average_order_value": average_order_value,
        "collection_rate": collection_rate,
        "average_completion_time": avg_completion_time,
        "on_time_delivery_rate": on_time_delivery_rate,
        "orders_by_company": orders_by_company,
        "top_performing_employees": top_performing_employees,
        "daily_performance": daily_performance,
    }


@router.post(
    "/offices/employees/{employee_id}/performance/",
    response=EmployeePerformanceSchema,
    auth=AuthBearer(),
)
def get_employee_performance(request, employee_id: int, date_range: DateRangeSchema):
    """Get performance report for a specific employee."""
    user = request.auth

    # Managers and masters can access any employee's performance
    # Employees can only access their own performance
    if user.role not in [Role.MANAGER, Role.MASTER] and user.id != employee_id:
        raise HttpError(
            403, "Forbidden: You can only access your own performance report."
        )

    # Get the office
    office = get_object_or_404(Office, id=user.office_id)

    # Get the employee
    employee = get_object_or_404(User, id=employee_id, office=office)

    # Filter orders by date range, office, and employee
    # Convert date objects to timezone-aware datetime objects
    start_date = timezone.make_aware(
        datetime.combine(date_range.start_date, datetime.min.time())
    )
    end_date = timezone.make_aware(
        datetime.combine(date_range.end_date, datetime.max.time())
    )

    orders = Order.objects.filter(
        office=office,
        assigned_to=employee,
        created_at__gte=start_date,
        created_at__lte=end_date,  # end_date already includes the full day
    )

    # Calculate total orders assigned
    total_orders_assigned = orders.count()

    # Calculate total orders completed
    completed_orders = orders.filter(
        order_handling_status=OrderHandlingStatus.COMPLETED
    )
    total_orders_completed = completed_orders.count()

    # Calculate completion rate
    completion_rate = (
        (total_orders_completed / total_orders_assigned) * 100
        if total_orders_assigned > 0
        else 0
    )

    # Calculate average completion time
    avg_completion_time = None
    on_time_delivery_count = 0

    for order in completed_orders:
        if order.assigned_at and order.updated_at:
            # If we have both timestamps, we can calculate completion time
            completion_time = (
                order.updated_at - order.assigned_at
            ).total_seconds() / 3600  # in hours
            if avg_completion_time is None:
                avg_completion_time = completion_time
            else:
                avg_completion_time = (avg_completion_time + completion_time) / 2

        # Check if order was delivered on time
        if (
            order.delivery_deadline_date
            and order.updated_at <= order.delivery_deadline_date
        ):
            on_time_delivery_count += 1

    # Calculate financial metrics
    total_revenue = (
        completed_orders.aggregate(Sum("total_price"))["total_price__sum"] or 0
    )
    average_order_value = (
        total_revenue / total_orders_completed if total_orders_completed > 0 else 0
    )

    # Calculate commission earned
    commission_rate = employee.commission_rate or 0
    commission_earned = (
        (total_revenue * commission_rate) / 100 if commission_rate > 0 else 0
    )

    # Calculate on-time delivery percentage
    on_time_delivery_percentage = (
        (on_time_delivery_count / total_orders_completed) * 100
        if total_orders_completed > 0
        else 0
    )

    # Calculate orders by status
    orders_by_status = dict(
        orders.values("order_handling_status")
        .annotate(count=Count("id"))
        .values_list("order_handling_status", "count")
    )

    # Calculate daily performance
    daily_performance = []
    current_date = start_date.date() if hasattr(start_date, "date") else start_date
    end_date_only = end_date.date() if hasattr(end_date, "date") else end_date

    while current_date <= end_date_only:
        next_date = current_date + timedelta(days=1)
        # Convert to timezone-aware datetime for filtering
        current_datetime = timezone.make_aware(
            datetime.combine(current_date, datetime.min.time())
        )
        next_datetime = timezone.make_aware(
            datetime.combine(next_date, datetime.min.time())
        )

        daily_orders = orders.filter(
            created_at__gte=current_datetime, created_at__lt=next_datetime
        )
        daily_completed = daily_orders.filter(
            order_handling_status=OrderHandlingStatus.COMPLETED
        )

        daily_performance.append(
            {
                "date": current_date.isoformat(),
                "total_orders": daily_orders.count(),
                "completed_orders": daily_completed.count(),
                "revenue": daily_completed.aggregate(Sum("total_price"))[
                    "total_price__sum"
                ]
                or 0,
            }
        )

        current_date = next_date

    # Prepare the response
    return {
        "employee_id": employee.id,
        "employee_name": employee.get_full_name(),
        "total_orders_assigned": total_orders_assigned,
        "total_orders_completed": total_orders_completed,
        "completion_rate": completion_rate,
        "average_completion_time": avg_completion_time,
        "total_revenue": total_revenue,
        "commission_earned": commission_earned,
        "average_order_value": average_order_value,
        "on_time_delivery_percentage": on_time_delivery_percentage,
        "orders_by_status": orders_by_status,
        "daily_performance": daily_performance,
    }


@router.get(
    "/offices/historical-activities",
    response=HistoricalActivityResponseSchema,
    auth=AuthBearer(),
)
async def get_historical_activities(request, page: int = 1, page_size: int = 20):
    """
    Get historical activity logs for the office (dashboard view).
    """

    user = request.auth

    cache_key = f"historical_activities_{user.office_id}_{page}_{page_size}"

    cached_data = cache.get(cache_key)
    if cached_data:
        return HistoricalActivityResponseSchema.from_orm(cached_data)

    # Only managers and master users can access historical activities
    if user.role not in [Role.MANAGER, Role.MASTER]:
        raise HttpError(
            403,
            "Forbidden: Only Managers and Master Users can access historical activities.",
        )

    # Validate pagination parameters
    if page < 1:
        page = 1
    if page_size < 1 or page_size > 100:
        page_size = 20

    # Calculate offset
    offset = (page - 1) * page_size

    # Get the office asynchronously
    office = await sync_to_async(get_object_or_404)(Office, id=user.office_id)

    # Define async query functions for better performance
    async def get_assignment_activities():
        """Get assignment history activities asynchronously."""
        assignments = await sync_to_async(list)(
            OrderAssignmentHistory.objects.filter(office=office)
            .select_related("order", "assigned_to", "assigned_by")
            .only(
                "id",
                "assigned_at",
                "assigned_by__first_name",
                "assigned_by__last_name",
                "assigned_to__first_name",
                "assigned_to__last_name",
                "assigned_by__id",
                "assigned_to__id",
                "order__id",
                "order__code",
                "order__customer_name",
            )
            .order_by("-assigned_at")[:500]  # Reduced limit for better performance
        )

        activities = []
        for assignment in assignments:
            activities.append(
                {
                    "id": f"assignment_{assignment.id}",
                    "timestamp": assignment.assigned_at,
                    "activity_type": "ORDER_ASSIGNMENT",
                    "user_name": assignment.assigned_by.get_full_name(),
                    "user_id": assignment.assigned_by.id,
                    "description": f"تم تعيين الطلب إلى {assignment.assigned_to.get_full_name()}",
                    "order_id": assignment.order.id,
                    "order_code": assignment.order.code,
                    "customer_name": assignment.order.customer_name,
                    "details": {
                        "assigned_to_name": assignment.assigned_to.get_full_name(),
                        "assigned_to_id": assignment.assigned_to.id,
                    },
                }
            )
        return activities

    async def get_status_activities():
        """Get status change history activities asynchronously."""
        statuses = await sync_to_async(list)(
            OrderHandlingStatusHistory.objects.filter(office=office)
            .select_related("order", "changed_by", "delivery_status")
            .only(
                "id",
                "created_at",
                "handling_status",
                "note",
                "changed_by__first_name",
                "changed_by__last_name",
                "changed_by__id",
                "order__id",
                "order__code",
                "order__customer_name",
                "delivery_status__name",
            )
            .order_by("-created_at")[:500]  # Reduced limit for better performance
        )

        activities = []
        for status in statuses:
            status_name = (
                status.get_handling_status_display()
                if status.handling_status
                else "غير معروف"
            )
            delivery_status_name = (
                status.delivery_status.name if status.delivery_status else None
            )

            description_parts = [f"تم تغيير حالة الطلب إلى {status_name}"]
            if delivery_status_name:
                description_parts.append(f"حالة التسليم: {delivery_status_name}")

            description = " - ".join(description_parts)

            details = {
                "old_status": None,
                "new_status": status.handling_status,
                "new_status_display": status_name,
            }

            if delivery_status_name:
                details["delivery_status"] = delivery_status_name

            if status.note:
                details["note"] = status.note

            activities.append(
                {
                    "id": f"status_{status.id}",
                    "timestamp": status.created_at,
                    "activity_type": "STATUS_CHANGE",
                    "user_name": status.changed_by.get_full_name(),
                    "user_id": status.changed_by.id,
                    "description": description,
                    "order_id": status.order.id,
                    "order_code": status.order.code,
                    "customer_name": status.order.customer_name,
                    "details": details,
                }
            )
        return activities

    # Execute both queries concurrently for better performance
    assignment_activities, status_activities = await asyncio.gather(
        get_assignment_activities(), get_status_activities()
    )

    # Combine and sort all activities by timestamp (most recent first)
    all_activities = assignment_activities + status_activities
    all_activities.sort(key=lambda x: x["timestamp"], reverse=True)

    # Calculate total count
    total_count = len(all_activities)

    # Apply pagination
    paginated_activities = all_activities[offset : offset + page_size]

    # Convert to schema format using list comprehension for better performance
    activities = [
        HistoricalActivitySchema(
            id=activity["id"],
            timestamp=activity["timestamp"],
            activity_type=activity["activity_type"],
            user_name=activity["user_name"],
            user_id=activity["user_id"],
            description=activity["description"],
            order_id=activity["order_id"],
            order_code=activity["order_code"],
            customer_name=activity["customer_name"],
            details=activity["details"],
        )
        for activity in paginated_activities
    ]

    # Check if there are more pages
    has_more = offset + page_size < total_count

    activity_res = HistoricalActivityResponseSchema(
        activities=activities,
        total_count=total_count,
        page=page,
        page_size=page_size,
        has_more=has_more,
    )

    cache.set(cache_key, activity_res.model_dump(), 300)

    return activity_res


@router.get(
    "/offices/historical-activities-v2",
    response=HistoricalActivityResponseSchema,
    auth=AuthBearer(),
)
async def get_historical_activities_v2(request, page: int = 1, page_size: int = 20):
    """
    Ultra-optimized version using database-level pagination and raw SQL for maximum performance.

    This version is recommended for offices with very large activity histories (>10k records).
    """
    user = request.auth

    # Only managers and master users can access historical activities
    if user.role not in [Role.MANAGER, Role.MASTER]:
        raise HttpError(
            403,
            "Forbidden: Only Managers and Master Users can access historical activities.",
        )

    # Validate pagination parameters
    if page < 1:
        page = 1
    if page_size < 1 or page_size > 100:
        page_size = 20

    # Calculate offset
    offset = (page - 1) * page_size

    # Get the office asynchronously
    office = await sync_to_async(get_object_or_404)(Office, id=user.office_id)

    # Use raw SQL for maximum performance with database-level sorting and pagination
    async def get_unified_activities():
        """Get all activities using a single optimized query with UNION ALL."""
        sql = """
        SELECT 
            'assignment_' || oah.id as id,
            oah.assigned_at as timestamp,
            'ORDER_ASSIGNMENT' as activity_type,
            (abu.first_name || ' ' || abu.last_name) as user_name,
            abu.id as user_id,
            ('تم تعيين الطلب إلى ' || atu.first_name || ' ' || atu.last_name) as description,
            o.id as order_id,
            o.code as order_code,
            o.customer_name,
            json_object(
                'assigned_to_name', (atu.first_name || ' ' || atu.last_name),
                'assigned_to_id', atu.id
            ) as details
        FROM order_assignment_history oah
        JOIN orders o ON oah.order_id = o.id
        JOIN users abu ON oah.assigned_by_id = abu.id
        JOIN users atu ON oah.assigned_to_id = atu.id
        WHERE oah.office_id = %s
        
        UNION ALL
        
        SELECT 
            'status_' || osh.id as id,
            osh.created_at as timestamp,
            'STATUS_CHANGE' as activity_type,
            (cu.first_name || ' ' || cu.last_name) as user_name,
            cu.id as user_id,
            ('تم تغيير حالة الطلب إلى ' || 
                CASE osh.handling_status
                    WHEN 'PENDING' THEN 'في الانتظار'
                    WHEN 'ASSIGNED' THEN 'مُعيَّن'
                    WHEN 'PROCESSING' THEN 'قيد المعالجة'
                    WHEN 'COMPLETED' THEN 'مكتمل'
                    ELSE 'غير معروف'
                END ||
                CASE WHEN ods.name IS NOT NULL THEN (' - حالة التسليم: ' || ods.name) ELSE '' END
            ) as description,
            o2.id as order_id,
            o2.code as order_code,
            o2.customer_name,
            json_object(
                'old_status', NULL,
                'new_status', osh.handling_status,
                'new_status_display', CASE osh.handling_status
                    WHEN 'PENDING' THEN 'في الانتظار'
                    WHEN 'ASSIGNED' THEN 'مُعيَّن'
                    WHEN 'PROCESSING' THEN 'قيد المعالجة'
                    WHEN 'COMPLETED' THEN 'مكتمل'
                    ELSE 'غير معروف'
                END,
                'delivery_status', ods.name,
                'note', osh.note
            ) as details
        FROM order_handling_status_history osh
        JOIN orders o2 ON osh.order_id = o2.id
        JOIN users cu ON osh.changed_by_id = cu.id
        LEFT JOIN order_delivery_statuses ods ON osh.delivery_status_id = ods.id
        WHERE osh.office_id = %s
        
        ORDER BY timestamp DESC
        LIMIT %s OFFSET %s
        """

        # Execute the query asynchronously
        def execute_query():
            with connection.cursor() as cursor:
                cursor.execute(sql, [office.id, office.id, page_size, offset])
                columns = [col[0] for col in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]

        return await sync_to_async(execute_query)()

    # Get count query for pagination
    async def get_total_count():
        """Get total count of activities for pagination."""
        assignment_count = await sync_to_async(
            OrderAssignmentHistory.objects.filter(office=office).count
        )()
        status_count = await sync_to_async(
            OrderHandlingStatusHistory.objects.filter(office=office).count
        )()
        return assignment_count + status_count

    # Execute both queries concurrently
    activities_data, total_count = await asyncio.gather(
        get_unified_activities(), get_total_count()
    )

    # Convert to schema format
    activities = []
    for activity_data in activities_data:
        # Parse JSON details if it's a string
        details = activity_data["details"]
        if isinstance(details, str):
            details = json.loads(details)

        activities.append(
            HistoricalActivitySchema(
                id=activity_data["id"],
                timestamp=activity_data["timestamp"],
                activity_type=activity_data["activity_type"],
                user_name=activity_data["user_name"],
                user_id=activity_data["user_id"],
                description=activity_data["description"],
                order_id=activity_data["order_id"],
                order_code=activity_data["order_code"],
                customer_name=activity_data["customer_name"],
                details=details,
            )
        )

    # Check if there are more pages
    has_more = offset + page_size < total_count

    return HistoricalActivityResponseSchema(
        activities=activities,
        total_count=total_count,
        page=page,
        page_size=page_size,
        has_more=has_more,
    )
