# Excel Import - User Guide

## Table of Contents
- [Getting Started](#getting-started)
- [Preparing Your Excel File](#preparing-your-excel-file)
- [Using the Import Feature](#using-the-import-feature)
- [Understanding Results](#understanding-results)
- [Supported File Formats](#supported-file-formats)
- [Column Naming Guide](#column-naming-guide)
- [Troubleshooting](#troubleshooting)
- [Best Practices](#best-practices)

## Getting Started

The Excel Import feature allows you to quickly import order data from Excel files without worrying about exact column positions or formatting. The system automatically detects your column headers and maps them to the appropriate fields.

### What You Need
- An Excel file (.xlsx or .xls) with order data
- Column headers in Arabic or English
- Basic order information (customer name and phone number are required)

### Key Benefits
- **No setup required** - just upload your file
- **Flexible format** - columns can be in any order
- **Multi-language support** - works with Arabic and English headers
- **Smart detection** - automatically finds and maps your columns
- **Detailed feedback** - clear reports on what was imported and any issues

## Preparing Your Excel File

### Basic Requirements

1. **Include column headers** in a single row
2. **Use clear, descriptive names** for your columns
3. **Keep data consistent** within each column
4. **Avoid empty rows** between headers and data

### Example of a Well-Formatted File

```
| م | الاسم      | رقم التليفون  | المركز    | قيمة الاوردر | رمز الشركة |
|---|-----------|-------------|----------|------------|----------|
| 1 | أحمد محمد   | 01234567890 | القاهرة   | 150.50     | ABC      |
| 2 | فاطمة علي   | 01987654321 | الجيزة    | 200.00     | XYZ      |
| 3 | محمد حسن    | 01555666777 | الإسكندرية| 175.25     | ABC      |
```

### Supported Column Types

#### Required Columns
- **Customer Name** (اسم العميل / Customer Name)
- **Phone Number** (رقم التليفون / Phone Number)

#### Optional Columns
- **Company Code** (رمز الشركة / Company Code)
- **Company Name** (اسم الشركة / Company Name)
- **Order Value** (قيمة الاوردر / Order Value)
- **Address** (العنوان / Address)
- **Serial Number** (م / Serial Number)
- **Representative** (المندوب / Representative)
- **Status** (الحالة / Status)
- **Notes** (ملاحظات / Notes)

## Using the Import Feature

### Step 1: Access the Import Page
1. Log in to your account
2. Navigate to **Orders** → **Import from Excel**
3. You'll see the file upload interface

### Step 2: Upload Your File
1. Click **"Choose File"** or drag and drop your Excel file
2. Select your .xlsx or .xls file
3. The system will automatically analyze your file

### Step 3: Configure Import Settings

#### Import Options
- **Use Intelligent Mapping**: ✅ Recommended (automatically detects columns)
- **Skip Duplicates**: ✅ Recommended (avoids importing duplicate orders)
- **Create Missing Companies**: Choose based on your needs
  - ✅ **Yes**: Creates new companies if they don't exist
  - ❌ **No**: Uses default company for unknown company codes

### Step 4: Review Detection Results
The system will show you:
- **Detected columns** and their mappings
- **Missing required fields** (if any)
- **Warnings** about optional fields not found

Example detection result:
```
✅ Column Mapping Detected:
   Customer Name ← Column 2: "الاسم"
   Phone Number ← Column 3: "رقم التليفون"
   Company Code ← Column 6: "رمز الشركة"
   Order Value ← Column 5: "قيمة الاوردر"
   Address ← Column 4: "المركز"

⚠️ Optional fields not found:
   - Representative name
   - Order status
```

### Step 5: Start Import
1. Review the detected mappings
2. Click **"Import Orders"**
3. Wait for processing to complete
4. Review the results

## Understanding Results

### Import Summary
After import completion, you'll see a summary like this:

```
📊 Import Summary:
   Total rows processed: 150
   Successfully imported: 142 orders
   Failed imports: 8 orders
   Skipped rows: 5 (empty rows)
   Success rate: 94.7%
   Processing time: 12.5 seconds
```

### Success Indicators
- **Green checkmarks** ✅ indicate successful operations
- **High success rate** (>90%) indicates good data quality
- **Fast processing** shows efficient file structure

### Error Reports
Errors are categorized and explained clearly:

#### Validation Errors
```
❌ Row 15: Invalid phone number format
   Field: customer_phone
   Value: "123"
   Solution: Phone numbers must be 10-15 digits
```

#### Missing Data Errors
```
❌ Row 28: Customer name is required
   Field: customer_name
   Value: (empty)
   Solution: Provide a customer name for this order
```

#### Company Errors
```
⚠️ Row 12: Company 'XYZ' not found
   Field: company_code
   Value: "XYZ"
   Action: Created new company (if enabled) or used default
```

## Supported File Formats

### Excel Formats
- **.xlsx** (Excel 2007 and later) ✅ Recommended
- **.xls** (Excel 97-2003) ✅ Supported

### File Size Limits
- **Maximum file size**: 50MB
- **Recommended size**: Under 10MB for best performance
- **Row limit**: No hard limit, but files with >10,000 rows may take longer

### Unsupported Formats
- ❌ CSV files (use Excel format instead)
- ❌ PDF files
- ❌ Word documents
- ❌ Google Sheets (export to Excel first)

## Column Naming Guide

### Arabic Column Names

| Field | Recommended Names | Alternative Names |
|-------|------------------|-------------------|
| Customer Name | الاسم | اسم، اسم العميل، اسم الزبون |
| Phone Number | رقم التليفون | التليفون، هاتف، موبايل، رقم الهاتف |
| Company Code | رمز الشركة | رمز، كود، كود الشركة |
| Company Name | اسم الشركة | الشركة، شركة، مؤسسة |
| Order Value | قيمة الاوردر | قيمة، مبلغ، سعر، المبلغ |
| Address | العنوان | عنوان، المركز، المنطقة، المكان |
| Serial Number | م | رقم، تسلسل، رقم تسلسلي |
| Representative | المندوب | مندوب، اسم المندوب، حساب المندوب |
| Status | الحالة | حالة، وضع، الوضع |

### English Column Names

| Field | Recommended Names | Alternative Names |
|-------|------------------|-------------------|
| Customer Name | Customer Name | Name, Client Name, Customer |
| Phone Number | Phone Number | Phone, Mobile, Telephone, Contact |
| Company Code | Company Code | Code, Comp Code, Company ID |
| Company Name | Company Name | Company, Organization, Business |
| Order Value | Order Value | Price, Amount, Total, Cost, Value |
| Address | Address | Location, Area, Region |
| Serial Number | Serial Number | Serial, Number, ID, Index |
| Representative | Representative | Rep, Sales Rep, Agent |
| Status | Status | State, Condition |

### Mixed Language Support
You can use both Arabic and English headers in the same file:
```
| Serial | الاسم | Phone Number | المركز | Order Value | رمز الشركة |
```

## Troubleshooting

### Common Issues and Solutions

#### 1. "No headers detected"
**Problem**: The system cannot find column headers in your file.

**Solutions**:
- Ensure headers are in a single row (not split across multiple rows)
- Check that headers contain recognizable text (not just numbers or symbols)
- Remove any merged cells in the header row
- Make sure the header row is within the first 15 rows of the file

#### 2. "Required fields missing"
**Problem**: Customer name or phone number columns not detected.

**Solutions**:
- Check column header spelling (use names from the guide above)
- Ensure headers are in Arabic or English
- Avoid abbreviations that might not be recognized
- Try alternative column names from the guide

#### 3. "Low success rate"
**Problem**: Many rows failed to import.

**Common causes and solutions**:
- **Invalid phone numbers**: Ensure phone numbers are 10-15 digits
- **Empty required fields**: Fill in customer names and phone numbers
- **Inconsistent data**: Check for typos and formatting issues
- **Wrong data types**: Ensure prices are numbers, not text

#### 4. "File too large"
**Problem**: File exceeds size limits.

**Solutions**:
- Split large files into smaller chunks (under 10MB each)
- Remove unnecessary columns or rows
- Save as .xlsx format (more efficient than .xls)

#### 5. "Company not found" warnings
**Problem**: Company codes in your file don't match existing companies.

**Solutions**:
- Enable "Create missing companies" option
- Check company code spelling and format
- Provide company names along with codes
- Contact admin to add missing companies

### Getting Help

If you continue to experience issues:

1. **Check the error messages** - they often contain specific solutions
2. **Review your file format** against the examples in this guide
3. **Try with a smaller sample** of your data first
4. **Contact support** with:
   - Your Excel file (if possible)
   - Screenshot of error messages
   - Description of what you expected vs. what happened

## Best Practices

### File Preparation
1. **Start with a clean template** - remove unnecessary formatting
2. **Use consistent naming** - stick to one language for headers
3. **Validate your data** - check phone numbers and required fields
4. **Test with small files** - try 10-20 rows first
5. **Keep backups** - save original files before making changes

### Data Quality
1. **Standardize phone numbers** - use consistent format (e.g., 01234567890)
2. **Clean customer names** - remove extra spaces and special characters
3. **Validate company codes** - ensure they match your system
4. **Check prices** - use numbers, not text with currency symbols
5. **Fill required fields** - don't leave customer names or phones empty

### Import Strategy
1. **Use intelligent mapping** - it's more flexible and accurate
2. **Enable duplicate skipping** - prevents accidental re-imports
3. **Review results carefully** - check error messages and warnings
4. **Import in batches** - for very large datasets, split into smaller files
5. **Monitor success rates** - aim for >90% success rate

### Ongoing Maintenance
1. **Standardize your templates** - create consistent Excel formats
2. **Train your team** - ensure everyone follows the same conventions
3. **Regular data cleanup** - maintain clean master data (companies, etc.)
4. **Monitor import patterns** - identify and fix recurring issues
5. **Keep documentation updated** - maintain your own naming conventions

---

*This user guide provides everything you need to successfully import order data from Excel files. The intelligent mapping system makes the process simple and flexible, adapting to your file format automatically.*
