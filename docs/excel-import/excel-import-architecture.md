# Intelligent Excel Import - Technical Architecture

## Table of Contents
- [System Overview](#system-overview)
- [Architecture Components](#architecture-components)
- [Data Flow](#data-flow)
- [Core Components](#core-components)
- [Integration Points](#integration-points)
- [Workflow Diagrams](#workflow-diagrams)
- [Database Schema](#database-schema)
- [Error Handling Strategy](#error-handling-strategy)

## System Overview

The Intelligent Excel Import system is built on a modular architecture that separates concerns between file parsing, column detection, data validation, and database operations. This design ensures maintainability, testability, and extensibility.

### High-Level Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │   API Gateway    │    │   File Storage  │
│                 │    │                  │    │                 │
│ • File Upload   │◄──►│ • Authentication │◄──►│ • Temp Files    │
│ • Progress      │    │ • Validation     │    │ • Cleanup       │
│ • Results       │    │ • Rate Limiting  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Excel Import Service Layer                   │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│ IntelligentCol- │ ExcelParser     │ BulkImport      │ Order     │
│ umnMapper       │ Service         │ Service         │ Service   │
│                 │                 │                 │           │
│ • Header        │ • File Reading  │ • Batch Insert  │ • CRUD    │
│   Detection     │ • Row Parsing   │ • Validation    │ • Search  │
│ • Pattern       │ • Data Mapping  │ • Error Handle  │ • Status  │
│   Matching      │ • Validation    │ • Transaction   │           │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
                                 │
                                 ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Database Layer                             │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│ Orders Table    │ Companies Table │ Import Logs     │ Users     │
│                 │                 │                 │           │
│ • Order Data    │ • Company Info  │ • Import Stats  │ • Auth    │
│ • Status        │ • Codes         │ • Error Logs    │ • Perms   │
│ • Timestamps    │ • Relationships │ • Audit Trail   │           │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
```

## Architecture Components

### 1. **Presentation Layer**
- **Frontend UI**: React/Vue components for file upload and results display
- **API Gateway**: Django REST framework endpoints with authentication
- **File Handling**: Temporary file storage and cleanup mechanisms

### 2. **Business Logic Layer**
- **IntelligentColumnMapper**: Core intelligence for column detection
- **ExcelParserService**: File parsing and data extraction
- **BulkImportService**: Batch processing and database operations
- **ValidationService**: Data validation and error reporting

### 3. **Data Access Layer**
- **Django ORM**: Database abstraction and query optimization
- **Model Relationships**: Order, Company, and User entity relationships
- **Transaction Management**: ACID compliance for bulk operations

### 4. **Infrastructure Layer**
- **File System**: Temporary file storage with automatic cleanup
- **Logging**: Comprehensive logging for debugging and monitoring
- **Error Handling**: Structured error reporting and recovery

## Data Flow

### Complete Import Workflow

```
graph TD
    A[User Uploads Excel File] --> B[API Receives File]
    B --> C[Save to Temporary Storage]
    C --> D[Initialize IntelligentColumnMapper]
    D --> E[Detect Header Row]
    E --> F[Map Columns to Fields]
    F --> G[Validate Mapping]
    G --> H{Mapping Valid?}
    H -->|No| I[Return Mapping Errors]
    H -->|Yes| J[Initialize ExcelParserService]
    J --> K[Parse Excel File]
    K --> L[Process Each Row]
    L --> M[Validate Row Data]
    M --> N{Row Valid?}
    N -->|No| O[Add to Error List]
    N -->|Yes| P[Add to Success List]
    O --> Q{More Rows?}
    P --> Q
    Q -->|Yes| L
    Q -->|No| R[Initialize BulkImportService]
    R --> S[Resolve Companies]
    S --> T[Generate Order Codes]
    T --> U[Bulk Insert to Database]
    U --> V[Return Import Results]
    V --> W[Cleanup Temporary Files]
    I --> W
    W --> X[Send Response to Frontend]
```

### Column Detection Flow

```
graph TD
    A[Read Excel File] --> B[Scan First 15 Rows]
    B --> C[For Each Row]
    C --> D[Count Text Values]
    D --> E[Check Header Patterns]
    E --> F{Looks Like Headers?}
    F -->|No| G[Next Row]
    F -->|Yes| H[Found Header Row]
    G --> I{More Rows?}
    I -->|Yes| C
    I -->|No| J[No Headers Found]
    H --> K[Extract Column Headers]
    K --> L[For Each Header]
    L --> M[Match Against Patterns]
    M --> N[Calculate Confidence Score]
    N --> O{Score > Threshold?}
    O -->|Yes| P[Map to Field]
    O -->|No| Q[Mark as Unmapped]
    P --> R{More Headers?}
    Q --> R
    R -->|Yes| L
    R -->|No| S[Validate Required Fields]
    S --> T[Return Mapping Result]
    J --> U[Return Error]
```

## Core Components

### 1. IntelligentColumnMapper

**Location**: `orders/intelligent_column_mapper.py`

**Responsibilities**:
- Header row detection in Excel files
- Pattern-based column mapping
- Multi-language support (Arabic/English)
- Mapping validation and confidence scoring

**Key Methods**:
```python
class IntelligentColumnMapper:
    def detect_header_row(self, df: pd.DataFrame) -> Optional[int]
    def map_columns(self, df: pd.DataFrame, header_row: int) -> Dict[str, int]
    def validate_mapping(self, column_mapping: Dict[str, int]) -> Dict[str, Any]
    def get_mapping_summary(self, column_mapping: Dict[str, int]) -> str
```

**Pattern Matching Algorithm**:
1. **Exact Match**: Header exactly matches a pattern
2. **Substring Match**: Pattern found within header text
3. **Similarity Match**: Fuzzy string matching with confidence scoring
4. **Confidence Filtering**: Only accept matches above 60% confidence

### 2. ExcelParserService

**Location**: `orders/excel_parser_service.py`

**Responsibilities**:
- Excel file reading and parsing
- Row-by-row data extraction
- Data type conversion and cleaning
- Error collection and reporting

**Key Methods**:
```python
class ExcelParserService:
    def parse_excel_file_intelligent(self, file_path: Path) -> ExcelImportResult
    def _iterate_data_rows_intelligent(self, df: pd.DataFrame) -> Iterator
    def _map_row_to_fields_intelligent(self, row: pd.Series) -> Dict[str, Any]
    def _process_row(self, row_num: int, row_data: Dict) -> ProcessedOrderData
```

**Data Processing Pipeline**:
1. **File Reading**: Load Excel file using pandas
2. **Header Detection**: Use IntelligentColumnMapper to find headers
3. **Row Iteration**: Process each data row sequentially
4. **Field Mapping**: Map Excel columns to database fields
5. **Data Validation**: Validate each field using Pydantic models
6. **Error Handling**: Collect and categorize validation errors

### 3. BulkImportService

**Location**: `orders/bulk_import_service.py`

**Responsibilities**:
- Company resolution and creation
- Order code generation
- Bulk database operations
- Transaction management

**Key Methods**:
```python
class BulkImportService:
    def import_orders(self, processed_orders: List[ProcessedOrderData]) -> ImportResult
    def _resolve_company_by_code(self, company_code: str) -> Optional[Company]
    def _generate_order_code(self, order_data: ProcessedOrderData) -> str
    def _bulk_create_orders(self, order_data_list: List[Dict]) -> List[Order]
```

**Import Process**:
1. **Company Resolution**: Find or create companies by code/name
2. **Order Code Generation**: Generate unique order codes
3. **Duplicate Detection**: Check for existing orders
4. **Batch Processing**: Insert orders in configurable batch sizes
5. **Error Recovery**: Handle partial failures gracefully

## Integration Points

### 1. **API Layer Integration**

**Endpoint**: `POST /orders/import-excel`

```python
@orderApi.post("/orders/import-excel", auth=AuthBearer())
def import_orders_from_excel(
    request,
    excel_file: UploadedFile = File(...),
    use_intelligent_mapping: bool = Form(True),
    # ... other parameters
):
```

**Integration Flow**:
1. **Authentication**: Verify user permissions
2. **File Validation**: Check file type and size
3. **Temporary Storage**: Save uploaded file
4. **Service Orchestration**: Coordinate parsing and import services
5. **Response Generation**: Return detailed results to frontend

### 2. **Database Integration**

**Models Involved**:
- `Order`: Main order entity
- `Company`: Company master data
- `User`: User authentication and permissions

**Relationships**:
```python
class Order(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    # ... other fields

class Company(models.Model):
    office = models.ForeignKey(Office, on_delete=models.CASCADE)
    code = models.CharField(max_length=20, unique=True)
    # ... other fields
```

### 3. **External System Integration**

**File Storage**:
- Temporary file handling with automatic cleanup
- Support for large file uploads (streaming)
- Memory-efficient processing for large datasets

**Logging and Monitoring**:
- Structured logging for all operations
- Performance metrics collection
- Error tracking and alerting

## Workflow Diagrams

### Error Handling Workflow

```
graph TD
    A[Error Occurs] --> B{Error Type?}
    B -->|File Error| C[Log File Error]
    B -->|Mapping Error| D[Log Mapping Error]
    B -->|Validation Error| E[Log Validation Error]
    B -->|Database Error| F[Log Database Error]
    
    C --> G[Add to Error Collection]
    D --> G
    E --> G
    F --> G
    
    G --> H{Error Count > Max?}
    H -->|Yes| I[Stop Processing]
    H -->|No| J[Continue Processing]
    
    I --> K[Return Partial Results]
    J --> L[Process Next Item]
    K --> M[Cleanup Resources]
    L --> N[Check for More Items]
    M --> O[Send Error Response]
    N --> P{More Items?}
    P -->|Yes| L
    P -->|No| Q[Return Final Results]
```

### Company Resolution Workflow

```
graph TD
    A[Process Order Row] --> B{Company Code Present?}
    B -->|Yes| C[Search by Company Code]
    B -->|No| D{Company Name Present?}
    
    C --> E{Company Found?}
    E -->|Yes| F[Use Found Company]
    E -->|No| G{Create Missing Companies?}
    
    D --> H[Search by Company Name]
    H --> I{Company Found?}
    I -->|Yes| F
    I -->|No| G
    
    G -->|Yes| J[Create New Company]
    G -->|No| K[Use Default Company]
    
    J --> L{Creation Successful?}
    L -->|Yes| F
    L -->|No| M[Log Error]
    
    F --> N[Assign to Order]
    K --> N
    M --> O[Skip Order]
    N --> P[Continue Processing]
    O --> P
```

## Database Schema

### Core Tables

```sql
-- Orders table
CREATE TABLE orders_order (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR(255) UNIQUE NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    customer_address TEXT,
    total_price DECIMAL(10,2),
    company_id BIGINT REFERENCES companies_company(id),
    created_by_id BIGINT REFERENCES auth_user(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Companies table
CREATE TABLE companies_company (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    office_id BIGINT REFERENCES offices_office(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Import logs table (for audit trail)
CREATE TABLE import_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES auth_user(id),
    file_name VARCHAR(255),
    total_rows INTEGER,
    successful_rows INTEGER,
    failed_rows INTEGER,
    mapping_used JSONB,
    errors JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Indexes for Performance

```sql
-- Order indexes
CREATE INDEX idx_orders_company ON orders_order(company_id);
CREATE INDEX idx_orders_created_by ON orders_order(created_by_id);
CREATE INDEX idx_orders_created_at ON orders_order(created_at);
CREATE INDEX idx_orders_code ON orders_order(code);

-- Company indexes
CREATE INDEX idx_companies_code ON companies_company(code);
CREATE INDEX idx_companies_office ON companies_company(office_id);

-- Import log indexes
CREATE INDEX idx_import_logs_user ON import_logs(user_id);
CREATE INDEX idx_import_logs_created_at ON import_logs(created_at);
```

## Error Handling Strategy

### Error Categories

1. **File Errors**
   - Invalid file format
   - Corrupted files
   - File too large
   - Permission issues

2. **Mapping Errors**
   - No headers detected
   - Required fields missing
   - Ambiguous mappings
   - Unsupported languages

3. **Validation Errors**
   - Invalid phone numbers
   - Missing required data
   - Data type mismatches
   - Business rule violations

4. **Database Errors**
   - Connection failures
   - Constraint violations
   - Transaction timeouts
   - Disk space issues

### Error Recovery Mechanisms

1. **Graceful Degradation**
   - Continue processing valid rows when some rows fail
   - Provide partial results with detailed error reports
   - Allow users to fix issues and re-import failed rows

2. **Transaction Management**
   - Use database transactions for consistency
   - Rollback on critical errors
   - Commit successful batches independently

3. **Resource Cleanup**
   - Automatic temporary file cleanup
   - Memory management for large files
   - Connection pooling and cleanup

4. **User Feedback**
   - Detailed error messages with row numbers
   - Suggestions for fixing common issues
   - Progress indicators for long-running imports

---

*This architecture ensures scalability, maintainability, and reliability while providing a seamless user experience for Excel data imports.*
