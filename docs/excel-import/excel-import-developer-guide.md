# Excel Import - Developer Guide

## Table of Contents
- [Getting Started](#getting-started)
- [Extending Column Patterns](#extending-column-patterns)
- [Adding New Languages](#adding-new-languages)
- [Custom Validation Rules](#custom-validation-rules)
- [Programmatic Usage](#programmatic-usage)
- [Testing Strategies](#testing-strategies)
- [Performance Optimization](#performance-optimization)
- [Debugging and Troubleshooting](#debugging-and-troubleshooting)

## Getting Started

### Prerequisites
```bash
# Required Python packages
pip install pandas openpyxl pydantic django

# Optional for development
pip install pytest pytest-django black flake8
```

### Project Structure
```
orders/
├── intelligent_column_mapper.py    # Core mapping logic
├── excel_parser_service.py         # File parsing service
├── bulk_import_service.py          # Database operations
├── excel_import_schemas.py         # Data models
└── api.py                          # REST endpoints

tests/
├── test_intelligent_mapping.py     # Mapping tests
├── test_excel_parser.py           # Parser tests
└── fixtures/                      # Test Excel files
```

### Development Setup
```bash
# Clone and setup
git clone <repository>
cd <project>
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt

# Run tests
python manage.py test orders.tests
```

## Extending Column Patterns

### Adding New Field Patterns

To add support for new fields, modify the `field_patterns` dictionary in `IntelligentColumnMapper`:

```python
# orders/intelligent_column_mapper.py
class IntelligentColumnMapper:
    def __init__(self):
        self.field_patterns = {
            # Existing patterns...
            
            # Add new field patterns
            'delivery_date': [
                'تاريخ التسليم', 'تاريخ الوصول', 'delivery_date', 'delivery',
                'due_date', 'expected_date', 'موعد التسليم'
            ],
            
            'priority': [
                'الأولوية', 'أولوية', 'priority', 'urgency', 'importance',
                'مستوى الأولوية', 'درجة الأهمية'
            ],
            
            'payment_method': [
                'طريقة الدفع', 'الدفع', 'payment', 'payment_method',
                'pay_type', 'نوع الدفع', 'وسيلة الدفع'
            ]
        }
```

### Pattern Matching Strategies

The system uses multiple matching strategies in order of preference:

1. **Exact Match** (Highest Priority)
```python
if header_clean == pattern_clean:
    return field_name  # Immediate return
```

2. **Substring Match** (Medium Priority)
```python
if pattern_clean in header_clean or header_clean in pattern_clean:
    score = len(pattern_clean) / max(len(header_clean), len(pattern_clean))
    # Consider if score > threshold
```

3. **Similarity Match** (Lowest Priority)
```python
from difflib import SequenceMatcher
similarity = SequenceMatcher(None, header_clean, pattern_clean).ratio()
# Consider if similarity > 0.8
```

### Custom Pattern Matching

For complex pattern matching, override the `_find_best_field_match` method:

```python
class CustomColumnMapper(IntelligentColumnMapper):
    def _find_best_field_match(self, header: str) -> Optional[str]:
        # Custom logic for specific business rules
        header_clean = header.strip().lower()
        
        # Handle special cases
        if 'رقم' in header_clean and 'هاتف' in header_clean:
            return 'customer_phone'
        
        if header_clean.startswith('كود') or header_clean.startswith('رمز'):
            return 'company_code'
        
        # Fall back to parent implementation
        return super()._find_best_field_match(header)
```

## Adding New Languages

### Step 1: Define Language Patterns

Create a new pattern set for your language:

```python
# Example: Adding French support
french_patterns = {
    'customer_name': ['nom', 'nom_client', 'client', 'nom_du_client'],
    'customer_phone': ['téléphone', 'tel', 'mobile', 'numéro'],
    'company_code': ['code', 'code_entreprise', 'référence'],
    'total_price': ['prix', 'montant', 'total', 'coût'],
    'customer_address': ['adresse', 'lieu', 'localisation'],
    # ... more patterns
}
```

### Step 2: Integrate Language Patterns

Modify the `IntelligentColumnMapper` to support multiple languages:

```python
class MultiLanguageColumnMapper(IntelligentColumnMapper):
    def __init__(self, languages=['arabic', 'english', 'french']):
        super().__init__()
        self.languages = languages
        self._load_language_patterns()
    
    def _load_language_patterns(self):
        """Load patterns for all specified languages."""
        base_patterns = self.field_patterns.copy()
        
        if 'french' in self.languages:
            for field, patterns in french_patterns.items():
                if field in base_patterns:
                    base_patterns[field].extend(patterns)
                else:
                    base_patterns[field] = patterns
        
        self.field_patterns = base_patterns
```

### Step 3: Language Detection

Add automatic language detection:

```python
def detect_language(self, headers: List[str]) -> str:
    """Detect the primary language of headers."""
    arabic_count = sum(1 for h in headers if self._contains_arabic(h))
    french_count = sum(1 for h in headers if self._contains_french(h))
    english_count = len(headers) - arabic_count - french_count
    
    if arabic_count > english_count and arabic_count > french_count:
        return 'arabic'
    elif french_count > english_count:
        return 'french'
    else:
        return 'english'

def _contains_arabic(self, text: str) -> bool:
    """Check if text contains Arabic characters."""
    import re
    return bool(re.search(r'[\u0600-\u06FF]', text))

def _contains_french(self, text: str) -> bool:
    """Check if text contains French-specific characters."""
    french_chars = 'àâäéèêëïîôöùûüÿç'
    return any(char in text.lower() for char in french_chars)
```

## Custom Validation Rules

### Field-Level Validation

Extend the `ProcessedOrderData` model with custom validators:

```python
from pydantic import validator, Field
from typing import Optional

class ProcessedOrderData(BaseModel):
    customer_name: str = Field(..., min_length=1, max_length=255)
    customer_phone: str = Field(..., min_length=10, max_length=20)
    total_price: Optional[Decimal] = Field(None, ge=0)
    
    @validator('customer_phone')
    def validate_phone_format(cls, v):
        """Custom phone validation for multiple formats."""
        import re
        
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', v)
        
        # Egyptian phone number patterns
        egyptian_patterns = [
            r'^01[0-2,5]\d{8}$',  # Mobile numbers
            r'^02\d{8}$',         # Cairo landline
            r'^03\d{7}$',         # Alexandria landline
        ]
        
        for pattern in egyptian_patterns:
            if re.match(pattern, digits_only):
                return digits_only
        
        # International format
        if len(digits_only) >= 10 and digits_only.startswith('20'):
            return digits_only
        
        raise ValueError(f'Invalid phone number format: {v}')
    
    @validator('total_price', pre=True)
    def parse_price(cls, v):
        """Parse price from various formats."""
        if v is None or v == '':
            return None
        
        # Handle string prices with currency symbols
        if isinstance(v, str):
            import re
            # Remove currency symbols and spaces
            cleaned = re.sub(r'[^\d.,]', '', v)
            # Handle comma as decimal separator
            cleaned = cleaned.replace(',', '.')
            try:
                return Decimal(cleaned)
            except:
                raise ValueError(f'Invalid price format: {v}')
        
        return Decimal(str(v))
```

### Business Rule Validation

Add custom business logic validation:

```python
class BusinessRuleValidator:
    def __init__(self, office):
        self.office = office
    
    def validate_order_data(self, order_data: ProcessedOrderData) -> List[str]:
        """Validate business rules for order data."""
        errors = []
        
        # Rule 1: Minimum order value
        if order_data.total_price and order_data.total_price < Decimal('10.00'):
            errors.append("Order value must be at least 10.00")
        
        # Rule 2: Company code format
        if order_data.company_code:
            if not re.match(r'^[A-Z0-9]{2,10}$', order_data.company_code):
                errors.append("Company code must be 2-10 alphanumeric characters")
        
        # Rule 3: Phone number region validation
        if order_data.customer_phone:
            if not self._is_valid_region_phone(order_data.customer_phone):
                errors.append("Phone number not valid for this region")
        
        return errors
    
    def _is_valid_region_phone(self, phone: str) -> bool:
        """Check if phone number is valid for the office region."""
        # Implementation depends on business requirements
        return True  # Placeholder
```

## Programmatic Usage

### Basic Usage

```python
from orders.excel_parser_service import parse_excel_orders_intelligent
from orders.excel_import_schemas import ExcelImportConfig

# Simple usage with defaults
result, orders = parse_excel_orders_intelligent('data.xlsx')

print(f"Imported {result.successful_imports} orders")
for order in orders[:5]:  # Show first 5
    print(f"Order: {order.customer_name} - {order.total_price}")
```

### Advanced Configuration

```python
# Custom configuration
config = ExcelImportConfig(
    batch_size=100,           # Process in batches of 100
    max_errors=50,            # Stop after 50 errors
    skip_empty_rows=True,     # Skip empty rows
    require_customer_name=True,
    require_customer_phone=True,
    require_company_code=False
)

# Parse with custom config
result, orders = parse_excel_orders_intelligent('data.xlsx', config)

# Handle results
if result.failed_imports > 0:
    print("Errors occurred:")
    for error in result.errors:
        print(f"Row {error['row_number']}: {error['error_message']}")
```

### Direct Column Mapping

```python
from orders.intelligent_column_mapper import create_intelligent_mapping

# Analyze file without importing
mapping, start_row, validation = create_intelligent_mapping('data.xlsx')

print("Detected mapping:")
for field, col_idx in mapping.items():
    print(f"{field} -> Column {col_idx}")

if not validation['valid']:
    print(f"Missing required fields: {validation['missing_required']}")
```

### Custom Processing Pipeline

```python
from orders.intelligent_column_mapper import IntelligentColumnMapper
from orders.excel_parser_service import ExcelParserService
import pandas as pd

# Custom processing pipeline
def custom_import_pipeline(file_path: str):
    # Step 1: Analyze file structure
    mapper = IntelligentColumnMapper()
    df = pd.read_excel(file_path, header=None)
    
    header_row = mapper.detect_header_row(df)
    if header_row is None:
        raise ValueError("No headers detected")
    
    # Step 2: Create mapping
    column_mapping = mapper.map_columns(df, header_row)
    validation = mapper.validate_mapping(column_mapping)
    
    if not validation['valid']:
        raise ValueError(f"Invalid mapping: {validation['missing_required']}")
    
    # Step 3: Custom data processing
    parser = ExcelParserService()
    processed_orders = []
    
    for row_idx in range(header_row + 1, len(df)):
        row = df.iloc[row_idx]
        row_data = {}
        
        # Map columns using detected mapping
        for field, col_idx in column_mapping.items():
            if col_idx < len(row):
                row_data[field] = row.iloc[col_idx]
        
        # Custom validation and processing
        if row_data.get('customer_name'):
            # Apply custom business logic here
            processed_orders.append(row_data)
    
    return processed_orders
```

## Testing Strategies

### Unit Tests

```python
# tests/test_intelligent_mapping.py
import pytest
import pandas as pd
from orders.intelligent_column_mapper import IntelligentColumnMapper

class TestIntelligentColumnMapper:
    def setup_method(self):
        self.mapper = IntelligentColumnMapper()
    
    def test_arabic_header_detection(self):
        """Test detection of Arabic headers."""
        data = [
            [None, None, None],
            ['م', 'الاسم', 'رقم التليفون'],
            [1, 'أحمد', '***********']
        ]
        df = pd.DataFrame(data)
        
        header_row = self.mapper.detect_header_row(df)
        assert header_row == 1
    
    def test_column_mapping(self):
        """Test column mapping functionality."""
        data = [
            ['م', 'الاسم', 'رقم التليفون', 'رمز الشركة'],
            [1, 'أحمد', '***********', 'ABC']
        ]
        df = pd.DataFrame(data)
        
        mapping = self.mapper.map_columns(df, 0)
        
        assert mapping['serial_number'] == 0
        assert mapping['customer_name'] == 1
        assert mapping['customer_phone'] == 2
        assert mapping['company_code'] == 3
    
    def test_validation(self):
        """Test mapping validation."""
        mapping = {
            'customer_name': 0,
            'customer_phone': 1
        }
        
        result = self.mapper.validate_mapping(mapping)
        assert result['valid'] == True
        assert len(result['missing_required']) == 0
```

### Integration Tests

```python
# tests/test_excel_import_integration.py
import pytest
import tempfile
import pandas as pd
from orders.excel_parser_service import parse_excel_orders_intelligent

class TestExcelImportIntegration:
    def create_test_excel(self, data, filename):
        """Create a temporary Excel file for testing."""
        df = pd.DataFrame(data)
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            df.to_excel(tmp.name, index=False, header=False)
            return tmp.name
    
    def test_arabic_excel_import(self):
        """Test importing Arabic Excel file."""
        data = [
            ['م', 'الاسم', 'رقم التليفون', 'رمز الشركة'],
            [1, 'أحمد محمد', '***********', 'ABC'],
            [2, 'فاطمة علي', '01987654321', 'XYZ']
        ]
        
        file_path = self.create_test_excel(data, 'test_arabic.xlsx')
        
        try:
            result, orders = parse_excel_orders_intelligent(file_path)
            
            assert result.successful_imports == 2
            assert len(orders) == 2
            assert orders[0].customer_name == 'أحمد محمد'
            assert orders[1].customer_name == 'فاطمة علي'
        finally:
            import os
            os.unlink(file_path)
```

### Performance Tests

```python
# tests/test_performance.py
import pytest
import time
import pandas as pd
from orders.excel_parser_service import parse_excel_orders_intelligent

class TestPerformance:
    def test_large_file_performance(self):
        """Test performance with large Excel files."""
        # Create large test dataset
        data = [['م', 'الاسم', 'رقم التليفون']]
        for i in range(10000):
            data.append([i, f'Customer {i}', f'0123456789{i%10}'])
        
        file_path = self.create_test_excel(data, 'large_test.xlsx')
        
        try:
            start_time = time.time()
            result, orders = parse_excel_orders_intelligent(file_path)
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            # Performance assertions
            assert processing_time < 30  # Should complete within 30 seconds
            assert result.successful_imports > 9000  # Most should succeed
            
            print(f"Processed {len(orders)} orders in {processing_time:.2f} seconds")
            print(f"Rate: {len(orders)/processing_time:.0f} orders/second")
        finally:
            import os
            os.unlink(file_path)
```

## Performance Optimization

### Memory Management

```python
# For large files, use chunked processing
def process_large_excel_file(file_path: str, chunk_size: int = 1000):
    """Process large Excel files in chunks to manage memory."""
    df = pd.read_excel(file_path, header=None)
    
    # Detect mapping once
    mapper = IntelligentColumnMapper()
    header_row = mapper.detect_header_row(df)
    column_mapping = mapper.map_columns(df, header_row)
    
    # Process in chunks
    total_rows = len(df) - header_row - 1
    processed_orders = []
    
    for start_idx in range(header_row + 1, len(df), chunk_size):
        end_idx = min(start_idx + chunk_size, len(df))
        chunk = df.iloc[start_idx:end_idx]
        
        # Process chunk
        chunk_orders = process_chunk(chunk, column_mapping)
        processed_orders.extend(chunk_orders)
        
        # Optional: yield progress
        progress = (end_idx - header_row - 1) / total_rows * 100
        print(f"Progress: {progress:.1f}%")
    
    return processed_orders
```

### Database Optimization

```python
# Bulk operations for better performance
from django.db import transaction

def bulk_import_optimized(orders_data: List[Dict], batch_size: int = 500):
    """Optimized bulk import with batching and transactions."""
    
    with transaction.atomic():
        # Pre-fetch companies to avoid N+1 queries
        company_codes = {order['company_code'] for order in orders_data 
                        if order.get('company_code')}
        companies = {c.code: c for c in Company.objects.filter(code__in=company_codes)}
        
        # Prepare order objects
        order_objects = []
        for order_data in orders_data:
            company = companies.get(order_data.get('company_code'))
            if company:
                order_objects.append(Order(
                    code=order_data['order_code'],
                    customer_name=order_data['customer_name'],
                    customer_phone=order_data['customer_phone'],
                    company=company,
                    # ... other fields
                ))
        
        # Bulk create in batches
        for i in range(0, len(order_objects), batch_size):
            batch = order_objects[i:i + batch_size]
            Order.objects.bulk_create(batch, ignore_conflicts=True)
```

## Debugging and Troubleshooting

### Enable Debug Logging

```python
import logging

# Configure logging for debugging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Enable specific loggers
logger = logging.getLogger('orders.intelligent_column_mapper')
logger.setLevel(logging.DEBUG)
```

### Common Issues and Solutions

1. **Headers Not Detected**
```python
# Debug header detection
def debug_header_detection(file_path: str):
    df = pd.read_excel(file_path, header=None)
    mapper = IntelligentColumnMapper()
    
    print("Analyzing first 10 rows:")
    for i in range(min(10, len(df))):
        row = df.iloc[i]
        text_count = sum(1 for val in row if pd.notna(val) and isinstance(val, str))
        looks_like_header = any(mapper._looks_like_header(str(val)) for val in row if pd.notna(val))
        
        print(f"Row {i}: {text_count} text values, looks_like_header: {looks_like_header}")
        print(f"  Values: {row.tolist()}")
```

2. **Mapping Confidence Issues**
```python
# Debug mapping confidence
def debug_mapping_confidence(header: str):
    mapper = IntelligentColumnMapper()
    
    print(f"Analyzing header: '{header}'")
    for field, patterns in mapper.field_patterns.items():
        for pattern in patterns:
            if pattern.lower() in header.lower():
                print(f"  Matches {field} pattern: '{pattern}'")
```

3. **Performance Issues**
```python
# Profile performance
import cProfile
import pstats

def profile_import(file_path: str):
    profiler = cProfile.Profile()
    profiler.enable()
    
    # Run import
    result, orders = parse_excel_orders_intelligent(file_path)
    
    profiler.disable()
    
    # Analyze results
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(20)  # Top 20 functions
```

---

*This developer guide provides comprehensive information for extending and maintaining the intelligent Excel import system. For additional support, refer to the test suites and example implementations in the codebase.*
