# Excel Import API Documentation

## Table of Contents
- [Overview](#overview)
- [Authentication](#authentication)
- [Endpoints](#endpoints)
- [Request/Response Examples](#requestresponse-examples)
- [Error Handling](#error-handling)
- [Integration Examples](#integration-examples)
- [Rate Limiting](#rate-limiting)
- [Best Practices](#best-practices)

## Overview

The Excel Import API provides intelligent Excel file processing capabilities with automatic column detection and mapping. The API supports both traditional fixed-column mapping and the new intelligent mapping system.

### Base URL
```
https://api.yourapp.com/api/v1
```

### Content Types
- **Request**: `multipart/form-data` (for file uploads)
- **Response**: `application/json`

## Authentication

All API endpoints require authentication using Bearer tokens.

### Headers
```http
Authorization: Bearer <your-access-token>
Content-Type: multipart/form-data
```

### Getting Access Tokens
```http
POST /auth/login
Content-Type: application/json

{
    "username": "your-username",
    "password": "your-password"
}
```

**Response:**
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600
}
```

## Endpoints

### Import Orders from Excel

**Endpoint:** `POST /orders/import-excel`

**Description:** Import orders from an Excel file using intelligent column detection or traditional mapping.

#### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `excel_file` | File | Yes | - | Excel file (.xlsx, .xls) to import |
| `use_intelligent_mapping` | Boolean | No | `true` | Enable intelligent column detection |
| `skip_duplicates` | Boolean | No | `true` | Skip orders with duplicate codes |
| `create_missing_companies` | Boolean | No | `false` | Create companies that don't exist |
| `default_company_id` | Integer | No | `null` | Default company for orders without company |

#### Request Example

```http
POST /orders/import-excel
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
Content-Type: multipart/form-data

--boundary
Content-Disposition: form-data; name="excel_file"; filename="orders.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

[Excel file binary data]
--boundary
Content-Disposition: form-data; name="use_intelligent_mapping"

true
--boundary
Content-Disposition: form-data; name="skip_duplicates"

true
--boundary
Content-Disposition: form-data; name="create_missing_companies"

false
--boundary--
```

#### Response Schema

```json
{
    "success": true,
    "message": "Import completed successfully",
    "data": {
        "import_summary": {
            "total_rows_processed": 150,
            "successful_imports": 142,
            "failed_imports": 8,
            "skipped_rows": 5,
            "success_rate": 94.7,
            "processing_time_seconds": 12.5
        },
        "column_mapping": {
            "customer_name": 1,
            "customer_phone": 2,
            "company_code": 3,
            "total_price": 4,
            "customer_address": 5
        },
        "errors": [
            {
                "row_number": 15,
                "error_type": "validation_error",
                "error_message": "Invalid phone number format",
                "field": "customer_phone",
                "value": "invalid-phone"
            }
        ],
        "warnings": [
            {
                "row_number": 23,
                "warning_type": "company_not_found",
                "warning_message": "Company 'ABC' not found, using default",
                "field": "company_code",
                "value": "ABC"
            }
        ]
    }
}
```

## Request/Response Examples

### Example 1: Successful Import with Intelligent Mapping

**Request:**
```bash
curl -X POST "https://api.yourapp.com/api/v1/orders/import-excel" \
  -H "Authorization: Bearer your-token" \
  -F "excel_file=@orders_arabic.xlsx" \
  -F "use_intelligent_mapping=true" \
  -F "create_missing_companies=true"
```

**Response:**
```json
{
    "success": true,
    "message": "Import completed successfully",
    "data": {
        "import_summary": {
            "total_rows_processed": 86,
            "successful_imports": 73,
            "failed_imports": 8,
            "skipped_rows": 5,
            "success_rate": 84.9,
            "processing_time_seconds": 3.2
        },
        "column_mapping": {
            "serial_number": 0,
            "customer_name": 1,
            "customer_phone": 2,
            "customer_address": 3,
            "total_price": 4,
            "company_code": 5,
            "status": 6,
            "representative_name": 7
        },
        "detected_headers": {
            "0": "م",
            "1": "الاسم",
            "2": "رقم التليفون",
            "3": "المركز",
            "4": "قيمة الاوردر",
            "5": "رمز الشركة",
            "6": "الحالة",
            "7": "حساب المندوب"
        },
        "errors": [
            {
                "row_number": 15,
                "error_type": "validation_error",
                "error_message": "Phone number must be 10-15 digits",
                "field": "customer_phone",
                "value": "123"
            },
            {
                "row_number": 28,
                "error_type": "validation_error",
                "error_message": "Customer name is required",
                "field": "customer_name",
                "value": null
            }
        ],
        "warnings": [
            {
                "row_number": 12,
                "warning_type": "company_created",
                "warning_message": "Created new company with code 'XYZ'",
                "field": "company_code",
                "value": "XYZ"
            }
        ]
    }
}
```

### Example 2: Mapping Validation Error

**Request:**
```bash
curl -X POST "https://api.yourapp.com/api/v1/orders/import-excel" \
  -H "Authorization: Bearer your-token" \
  -F "excel_file=@invalid_format.xlsx" \
  -F "use_intelligent_mapping=true"
```

**Response:**
```json
{
    "success": false,
    "message": "Column mapping validation failed",
    "error": {
        "error_type": "mapping_error",
        "error_code": "REQUIRED_FIELDS_MISSING",
        "details": {
            "missing_required_fields": ["customer_name", "customer_phone"],
            "detected_fields": ["serial_number", "notes", "date"],
            "suggestions": [
                "Ensure your Excel file contains customer name column",
                "Ensure your Excel file contains phone number column",
                "Check column headers for typos or different languages"
            ]
        }
    }
}
```

### Example 3: File Format Error

**Request:**
```bash
curl -X POST "https://api.yourapp.com/api/v1/orders/import-excel" \
  -H "Authorization: Bearer your-token" \
  -F "excel_file=@document.pdf"
```

**Response:**
```json
{
    "success": false,
    "message": "Invalid file format",
    "error": {
        "error_type": "file_error",
        "error_code": "INVALID_FILE_FORMAT",
        "details": {
            "supported_formats": [".xlsx", ".xls"],
            "received_format": ".pdf",
            "file_size": 1024000
        }
    }
}
```

## Error Handling

### Error Response Structure

All error responses follow this structure:

```json
{
    "success": false,
    "message": "Human-readable error message",
    "error": {
        "error_type": "category_of_error",
        "error_code": "SPECIFIC_ERROR_CODE",
        "details": {
            // Additional error-specific information
        }
    }
}
```

### Error Types and Codes

#### File Errors
| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `INVALID_FILE_FORMAT` | Unsupported file format | 400 |
| `FILE_TOO_LARGE` | File exceeds size limit | 413 |
| `FILE_CORRUPTED` | File cannot be read | 400 |
| `FILE_EMPTY` | File contains no data | 400 |

#### Mapping Errors
| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `REQUIRED_FIELDS_MISSING` | Required columns not detected | 400 |
| `NO_HEADERS_DETECTED` | Cannot find column headers | 400 |
| `AMBIGUOUS_MAPPING` | Multiple columns match same field | 400 |

#### Validation Errors
| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `VALIDATION_FAILED` | Data validation errors | 400 |
| `BUSINESS_RULE_VIOLATION` | Business logic validation failed | 400 |
| `DATA_TYPE_MISMATCH` | Invalid data types | 400 |

#### Authentication Errors
| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `INVALID_TOKEN` | Invalid or expired token | 401 |
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions | 403 |

#### Server Errors
| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `DATABASE_ERROR` | Database operation failed | 500 |
| `PROCESSING_TIMEOUT` | Import took too long | 504 |
| `INTERNAL_ERROR` | Unexpected server error | 500 |

### Error Handling Best Practices

1. **Always check the `success` field** before processing response data
2. **Use `error_code` for programmatic handling** rather than parsing messages
3. **Display `message` to users** for human-readable feedback
4. **Log `details` for debugging** when errors occur
5. **Implement retry logic** for temporary errors (5xx status codes)

## Integration Examples

### JavaScript/React Integration

```javascript
// React component for Excel import
import React, { useState } from 'react';

const ExcelImportComponent = () => {
    const [file, setFile] = useState(null);
    const [importing, setImporting] = useState(false);
    const [result, setResult] = useState(null);
    const [error, setError] = useState(null);

    const handleImport = async () => {
        if (!file) return;

        setImporting(true);
        setError(null);

        const formData = new FormData();
        formData.append('excel_file', file);
        formData.append('use_intelligent_mapping', 'true');
        formData.append('create_missing_companies', 'true');

        try {
            const response = await fetch('/api/v1/orders/import-excel', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                setResult(data.data);
            } else {
                setError(data.error);
            }
        } catch (err) {
            setError({
                error_type: 'network_error',
                message: 'Failed to connect to server'
            });
        } finally {
            setImporting(false);
        }
    };

    return (
        <div>
            <input
                type="file"
                accept=".xlsx,.xls"
                onChange={(e) => setFile(e.target.files[0])}
            />
            <button onClick={handleImport} disabled={!file || importing}>
                {importing ? 'Importing...' : 'Import Excel'}
            </button>

            {result && (
                <div className="success">
                    <h3>Import Successful!</h3>
                    <p>Imported {result.import_summary.successful_imports} orders</p>
                    <p>Success rate: {result.import_summary.success_rate}%</p>
                </div>
            )}

            {error && (
                <div className="error">
                    <h3>Import Failed</h3>
                    <p>{error.message}</p>
                    {error.details && (
                        <ul>
                            {error.details.suggestions?.map((suggestion, i) => (
                                <li key={i}>{suggestion}</li>
                            ))}
                        </ul>
                    )}
                </div>
            )}
        </div>
    );
};
```

### Python Integration

```python
import requests
import json

class ExcelImportClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.token = token
        self.headers = {
            'Authorization': f'Bearer {token}'
        }
    
    def import_excel(self, file_path, **options):
        """Import Excel file with options."""
        url = f"{self.base_url}/orders/import-excel"
        
        # Default options
        data = {
            'use_intelligent_mapping': 'true',
            'skip_duplicates': 'true',
            'create_missing_companies': 'false',
            **options
        }
        
        with open(file_path, 'rb') as f:
            files = {'excel_file': f}
            response = requests.post(url, headers=self.headers, data=data, files=files)
        
        return response.json()
    
    def handle_import_result(self, result):
        """Handle import result with error checking."""
        if result['success']:
            summary = result['data']['import_summary']
            print(f"✅ Import successful!")
            print(f"   Processed: {summary['total_rows_processed']} rows")
            print(f"   Successful: {summary['successful_imports']} orders")
            print(f"   Failed: {summary['failed_imports']} orders")
            print(f"   Success rate: {summary['success_rate']:.1f}%")
            
            # Handle errors and warnings
            if result['data']['errors']:
                print(f"\n⚠️  Errors ({len(result['data']['errors'])}):")
                for error in result['data']['errors'][:5]:
                    print(f"   Row {error['row_number']}: {error['error_message']}")
            
            return True
        else:
            error = result['error']
            print(f"❌ Import failed: {result['message']}")
            print(f"   Error type: {error['error_type']}")
            print(f"   Error code: {error['error_code']}")
            
            if 'suggestions' in error.get('details', {}):
                print("   Suggestions:")
                for suggestion in error['details']['suggestions']:
                    print(f"   - {suggestion}")
            
            return False

# Usage example
client = ExcelImportClient('https://api.yourapp.com/api/v1', 'your-token')

result = client.import_excel(
    'orders.xlsx',
    use_intelligent_mapping='true',
    create_missing_companies='true'
)

client.handle_import_result(result)
```

## Rate Limiting

The API implements rate limiting to ensure fair usage and system stability.

### Limits
- **Authenticated users**: 100 requests per hour
- **File uploads**: 10 files per hour
- **File size limit**: 50MB per file

### Rate Limit Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

### Rate Limit Exceeded Response
```json
{
    "success": false,
    "message": "Rate limit exceeded",
    "error": {
        "error_type": "rate_limit_error",
        "error_code": "RATE_LIMIT_EXCEEDED",
        "details": {
            "limit": 100,
            "reset_time": "2024-01-01T12:00:00Z",
            "retry_after": 3600
        }
    }
}
```

## Best Practices

### 1. File Preparation
- **Use clear column headers** in Arabic or English
- **Place headers in a single row** (avoid merged cells)
- **Keep data consistent** within each column
- **Remove empty rows** between headers and data

### 2. Error Handling
- **Always check the `success` field** before processing data
- **Implement retry logic** for temporary failures
- **Log detailed error information** for debugging
- **Provide user-friendly error messages** in the UI

### 3. Performance
- **Limit file sizes** to reasonable amounts (< 50MB)
- **Process large files in chunks** if possible
- **Use intelligent mapping** for better success rates
- **Monitor processing times** and implement timeouts

### 4. Security
- **Validate file types** before upload
- **Scan files for malware** if required
- **Use HTTPS** for all API calls
- **Implement proper authentication** and authorization

### 5. User Experience
- **Show progress indicators** for long operations
- **Provide clear feedback** on mapping results
- **Allow users to preview** detected mappings
- **Offer suggestions** for fixing common issues

---

*This API documentation provides comprehensive information for integrating with the intelligent Excel import system. For additional support, refer to the developer guide and example implementations.*
