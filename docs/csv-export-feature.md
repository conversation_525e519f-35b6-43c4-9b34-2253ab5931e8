# CSV Export Feature Documentation

## Overview

The CSV Export feature provides a simple, lightweight alternative to Excel export. It generates standard CSV files that can be opened in any spreadsheet application, text editor, or imported into other systems. This feature is perfect for users who need quick data export without the complexity of Excel formatting.

## Key Features

### 1. **Multiple Export Formats**
- **English Headers**: Standard CSV with English column headers
- **Arabic Headers**: CSV with Arabic column headers for local use
- **UTF-8 with BOM**: Proper encoding for Excel compatibility

### 2. **Export Types**
- **Selective Export**: Export specific orders by their IDs
- **Bulk Export**: Export orders with advanced filtering capabilities
- **Same Filtering**: Uses the same advanced filters as Excel export

### 3. **Advantages over Excel**
- **Smaller file size**: ~89x smaller than Excel files
- **Universal compatibility**: Opens in any text editor or spreadsheet app
- **Faster generation**: No template processing required
- **Simple format**: Easy to parse programmatically

## API Endpoints

### 1. Selective CSV Export
**Endpoint**: `POST /orders/export-csv/selective`

**Request Body**:
```json
{
    "export_type": "selective",
    "order_ids": [1, 2, 3, 4, 5],
    "filename": "selected_orders.csv"
}
```

**Response**: CSV file download with English headers

### 2. Bulk CSV Export
**Endpoint**: `POST /orders/export-csv/bulk`

**Request Body**:
```json
{
    "export_type": "bulk",
    "filters": {
        "created_date_range": {
            "start_date": "2025-06-01",
            "end_date": "2025-06-20"
        },
        "status": ["PENDING", "COMPLETED"],
        "min_total_price": 100.0
    },
    "filename": "bulk_export.csv",
    "limit": 1000
}
```

**Response**: CSV file download with English headers

### 3. Bulk CSV Export (Arabic Headers)
**Endpoint**: `POST /orders/export-csv/bulk-arabic`

**Request Body**: Same as bulk export

**Response**: CSV file download with Arabic headers

## CSV Format Structure

### English Headers Format
```csv
"Order ID","Order Code","Customer Name","Customer Phone","Customer Address","Company","Total Price","Status","Assigned To","Created Date","Updated Date","Notes"
"1","ORD-001","أحمد محمد","01234567890","القاهرة","شركة ABC","250.0","COMPLETED","أحمد علي","2025-06-20 10:30:00","2025-06-20 15:45:00","ملاحظات الطلب"
```

### Arabic Headers Format
```csv
"رقم الطلب","كود الطلب","اسم العميل","رقم الهاتف","العنوان","الشركة","السعر الإجمالي","الحالة","مُعين إلى","تاريخ الإنشاء","تاريخ التحديث","ملاحظات"
"1","ORD-001","أحمد محمد","01234567890","القاهرة","شركة ABC","250.0","COMPLETED","أحمد علي","2025-06-20 10:30:00","2025-06-20 15:45:00","ملاحظات الطلب"
```

## Column Mapping

| Column | English Header | Arabic Header | Order Field | Data Type |
|--------|----------------|---------------|-------------|-----------|
| 1 | Order ID | رقم الطلب | id | Integer |
| 2 | Order Code | كود الطلب | code | String |
| 3 | Customer Name | اسم العميل | customer_name | String |
| 4 | Customer Phone | رقم الهاتف | customer_phone | String |
| 5 | Customer Address | العنوان | customer_address | String |
| 6 | Company | الشركة | customer_company.name | String |
| 7 | Total Price | السعر الإجمالي | total_price | Float |
| 8 | Status | الحالة | order_handling_status | String |
| 9 | Assigned To | مُعين إلى | assigned_to.get_full_name() | String |
| 10 | Created Date | تاريخ الإنشاء | created_at | DateTime |
| 11 | Updated Date | تاريخ التحديث | updated_at | DateTime |
| 12 | Notes | ملاحظات | notes | String |

## Usage Examples

### JavaScript/Frontend Integration

```javascript
// Selective CSV export
const exportSelectedCSV = async (orderIds) => {
    const response = await fetch('/api/orders/export-csv/selective', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            export_type: 'selective',
            order_ids: orderIds,
            filename: 'selected_orders.csv'
        })
    });
    
    if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'selected_orders.csv';
        a.click();
    }
};

// Bulk CSV export with English headers
const exportBulkCSV = async (filters) => {
    const response = await fetch('/api/orders/export-csv/bulk', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            export_type: 'bulk',
            filters: filters,
            limit: 1000
        })
    });
    
    if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'bulk_export.csv';
        a.click();
    }
};

// Bulk CSV export with Arabic headers
const exportBulkCSVArabic = async (filters) => {
    const response = await fetch('/api/orders/export-csv/bulk-arabic', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            export_type: 'bulk',
            filters: filters,
            limit: 1000
        })
    });
    
    if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'bulk_export_arabic.csv';
        a.click();
    }
};
```

### Python Integration

```python
import requests

class OrderCSVExportClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {token}'}
    
    def export_selective_csv(self, order_ids, filename=None):
        """Export specific orders to CSV."""
        url = f"{self.base_url}/orders/export-csv/selective"
        data = {
            'export_type': 'selective',
            'order_ids': order_ids,
            'filename': filename
        }
        
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.ok:
            filename = filename or f'export_{len(order_ids)}_orders.csv'
            with open(filename, 'wb') as f:
                f.write(response.content)
            return filename
        else:
            raise Exception(f"Export failed: {response.text}")
    
    def export_bulk_csv(self, filters, filename=None, limit=None, arabic_headers=False):
        """Export orders with filters to CSV."""
        endpoint = 'bulk-arabic' if arabic_headers else 'bulk'
        url = f"{self.base_url}/orders/export-csv/{endpoint}"
        
        data = {
            'export_type': 'bulk',
            'filters': filters,
            'filename': filename,
            'limit': limit
        }
        
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.ok:
            filename = filename or f'bulk_export{"_arabic" if arabic_headers else ""}.csv'
            with open(filename, 'wb') as f:
                f.write(response.content)
            return filename
        else:
            raise Exception(f"Export failed: {response.text}")

# Usage
client = OrderCSVExportClient('https://api.example.com', 'your-token')

# Export specific orders
client.export_selective_csv([1, 2, 3, 4, 5], 'my_orders.csv')

# Export with filters (English headers)
filters = {
    'status': ['COMPLETED'],
    'min_total_price': 100.0
}
client.export_bulk_csv(filters, 'filtered_orders.csv', limit=1000)

# Export with filters (Arabic headers)
client.export_bulk_csv(filters, 'filtered_orders_arabic.csv', limit=1000, arabic_headers=True)
```

### cURL Examples

```bash
# Selective CSV export
curl -X POST "/api/orders/export-csv/selective" \
  -H "Authorization: Bearer token" \
  -H "Content-Type: application/json" \
  -d '{
    "export_type": "selective",
    "order_ids": [1, 2, 3],
    "filename": "selected.csv"
  }' \
  --output selected.csv

# Bulk CSV export (English)
curl -X POST "/api/orders/export-csv/bulk" \
  -H "Authorization: Bearer token" \
  -H "Content-Type: application/json" \
  -d '{
    "export_type": "bulk",
    "filters": {"status": ["COMPLETED"]},
    "limit": 100
  }' \
  --output bulk.csv

# Bulk CSV export (Arabic)
curl -X POST "/api/orders/export-csv/bulk-arabic" \
  -H "Authorization: Bearer token" \
  -H "Content-Type: application/json" \
  -d '{
    "export_type": "bulk",
    "filters": {},
    "limit": 100
  }' \
  --output bulk_arabic.csv
```

## CSV vs Excel Comparison

| Feature | CSV | Excel |
|---------|-----|-------|
| **File Size** | ~1.9KB for 10 orders | ~166KB for 10 orders |
| **Size Ratio** | 1x | 89x larger |
| **Generation Speed** | Very fast | Slower (template processing) |
| **Compatibility** | Universal | Excel/LibreOffice |
| **Formatting** | Plain text | Rich formatting |
| **Arabic Support** | UTF-8 with BOM | Native |
| **Template Matching** | No | Yes (exact template format) |
| **Use Case** | Data export/import | Presentation/reporting |

## When to Use CSV vs Excel

### Use CSV When:
- **Quick data export** for analysis
- **System integration** (importing to other systems)
- **Large datasets** (better performance)
- **Simple data viewing** in text editors
- **Programmatic processing** of data
- **Storage efficiency** is important

### Use Excel When:
- **Presentation** to stakeholders
- **Template compliance** is required
- **Rich formatting** is needed
- **Arabic template matching** is essential
- **Representative name** field is required

## Technical Details

### Encoding
- **UTF-8 with BOM**: Ensures proper display of Arabic text in Excel
- **CSV quoting**: All fields are quoted to handle commas and special characters
- **Line endings**: Standard CRLF for Windows compatibility

### Performance
- **Memory efficient**: Streams data directly to response
- **Fast generation**: No template processing overhead
- **Small files**: Minimal overhead compared to Excel

### Security
- **Same permissions**: Uses identical access control as Excel export
- **Office isolation**: Users can only export from their office
- **Rate limiting**: Same rate limits as other export endpoints

---

*The CSV export feature provides a fast, lightweight alternative to Excel export while maintaining the same powerful filtering capabilities and security features.*
