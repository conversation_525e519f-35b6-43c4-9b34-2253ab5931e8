# Excel Export Feature Documentation

## Overview

The Excel Export feature generates .xlsx files that match the exact format and structure of the Arabic template file. It supports both selective export (specific orders) and bulk export (filtered orders) with comprehensive filtering capabilities.

## Key Features

### 1. **Template-Based Export**
- **Exact Format Matching**: Generated files match the template structure precisely
- **Arabic Headers**: Maintains all Arabic column headers and formatting
- **Representative Name**: Automatically populates "اسم المندوب" with office + user name
- **Date Integration**: Includes export date in Arabic format

### 2. **Export Types**
- **Selective Export**: Export specific orders by their IDs
- **Bulk Export**: Export orders with advanced filtering capabilities
- **Preview Mode**: Preview what will be exported before generating the file

### 3. **Advanced Filtering**
- Date ranges (created_date, updated_date)
- Order status filtering
- Representative/user filtering
- Company filtering (by ID or code)
- Customer name/phone search
- Price range filtering
- Office-based filtering

## Template Structure

The export matches this exact Arabic template format:

```
Row 3: [Representative Name] (اسم المندوب)
Row 4: [Date Label] (التــــــــاريـــــــــــــــــــــــــــخ)
Row 6: Headers
  Column 1: (Empty)
  Column 2: م (Serial Number)
  Column 3: الاسم (Customer Name)
  Column 4: التليفون (Phone Number)
  Column 5: الشركه (Company)
  Column 6: السعر (Price)
  Column 7: الحاله (Status/Notes)
  Column 8: حالة أولي (Initial Status/Amount)
```

## API Endpoints

### 1. Selective Export
**Endpoint**: `POST /orders/export-excel/selective`

**Request Body**:
```json
{
    "export_type": "selective",
    "order_ids": [1, 2, 3, 4, 5],
    "filename": "selected_orders.xlsx",
    "export_date": "2025-06-20"
}
```

**Response**: Excel file download

### 2. Bulk Export
**Endpoint**: `POST /orders/export-excel/bulk`

**Request Body**:
```json
{
    "export_type": "bulk",
    "filters": {
        "created_date_range": {
            "start_date": "2025-06-01",
            "end_date": "2025-06-20"
        },
        "status": ["pending", "completed"],
        "company_codes": ["ABC", "XYZ"],
        "min_total_price": 100.0,
        "max_total_price": 1000.0
    },
    "filename": "bulk_export.xlsx",
    "limit": 1000
}
```

**Response**: Excel file download

### 3. Export Preview
**Endpoint**: `POST /orders/export-excel/preview`

**Request Body**: Same as bulk export

**Response**:
```json
{
    "success": true,
    "total_orders": 150,
    "sample_orders": [
        {
            "id": 1,
            "code": "ORD-001",
            "customer_name": "أحمد محمد",
            "customer_phone": "01234567890",
            "company": "شركة ABC",
            "total_price": 250.0,
            "status": "completed",
            "created_at": "2025-06-20T10:30:00Z"
        }
    ],
    "estimated_file_size_mb": 0.08,
    "filters_applied": {
        "status": ["completed"],
        "min_total_price": 100.0
    }
}
```

## Data Mapping

### Order Fields → Excel Columns

| Excel Column | Arabic Header | Order Field | Notes |
|--------------|---------------|-------------|-------|
| Column 1 | (Empty) | - | Space for first row only |
| Column 2 | م | Auto-generated | Sequential numbering |
| Column 3 | الاسم | customer_name | Customer name |
| Column 4 | التليفون | customer_phone | Phone number |
| Column 5 | الشركه | customer_company.code | Company code/name |
| Column 6 | السعر | total_price | Order price |
| Column 7 | الحاله | order_handling_status + notes | Status and notes |
| Column 8 | حالة أولي | total_price | Initial amount |

### Representative Name Format
```
{office_name} - {user_full_name}
```
Example: "Cairo Office - Ahmed Mohamed"

## Filter Options

### Date Range Filters
```json
{
    "created_date_range": {
        "start_date": "2025-06-01",
        "end_date": "2025-06-20"
    },
    "updated_date_range": {
        "start_date": "2025-06-15",
        "end_date": "2025-06-20"
    }
}
```

### Status Filters
```json
{
    "status": ["pending", "confirmed", "in_progress", "completed", "cancelled"]
}
```

### User Filters
```json
{
    "assigned_to_ids": [1, 2, 3],
    "created_by_ids": [4, 5, 6]
}
```

### Company Filters
```json
{
    "company_ids": [1, 2, 3],
    "company_codes": ["ABC", "XYZ", "DEF"]
}
```

### Customer Filters
```json
{
    "customer_name_contains": "أحمد",
    "customer_phone_contains": "0123"
}
```

### Price Filters
```json
{
    "min_total_price": 100.0,
    "max_total_price": 1000.0
}
```

### Additional Filters
```json
{
    "office_ids": [1, 2],
    "has_notes": true,
    "limit": 1000
}
```

## Usage Examples

### JavaScript/Frontend Integration

```javascript
// Selective export
const exportSelected = async (orderIds) => {
    const response = await fetch('/api/orders/export-excel/selective', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            export_type: 'selective',
            order_ids: orderIds,
            filename: 'selected_orders.xlsx'
        })
    });
    
    if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'selected_orders.xlsx';
        a.click();
    }
};

// Bulk export with filters
const exportWithFilters = async (filters) => {
    const response = await fetch('/api/orders/export-excel/bulk', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            export_type: 'bulk',
            filters: filters,
            limit: 1000
        })
    });
    
    if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'bulk_export.xlsx';
        a.click();
    }
};

// Preview before export
const previewExport = async (filters) => {
    const response = await fetch('/api/orders/export-excel/preview', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            export_type: 'bulk',
            filters: filters
        })
    });
    
    const result = await response.json();
    console.log(`Will export ${result.total_orders} orders`);
    console.log(`Estimated size: ${result.estimated_file_size_mb} MB`);
    
    return result;
};
```

### Python Integration

```python
import requests

class OrderExportClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {token}'}
    
    def export_selective(self, order_ids, filename=None):
        """Export specific orders by IDs."""
        url = f"{self.base_url}/orders/export-excel/selective"
        data = {
            'export_type': 'selective',
            'order_ids': order_ids,
            'filename': filename
        }
        
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.ok:
            filename = filename or f'export_{len(order_ids)}_orders.xlsx'
            with open(filename, 'wb') as f:
                f.write(response.content)
            return filename
        else:
            raise Exception(f"Export failed: {response.text}")
    
    def export_bulk(self, filters, filename=None, limit=None):
        """Export orders with filters."""
        url = f"{self.base_url}/orders/export-excel/bulk"
        data = {
            'export_type': 'bulk',
            'filters': filters,
            'filename': filename,
            'limit': limit
        }
        
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.ok:
            filename = filename or 'bulk_export.xlsx'
            with open(filename, 'wb') as f:
                f.write(response.content)
            return filename
        else:
            raise Exception(f"Export failed: {response.text}")
    
    def preview_export(self, filters):
        """Preview export results."""
        url = f"{self.base_url}/orders/export-excel/preview"
        data = {
            'export_type': 'bulk',
            'filters': filters
        }
        
        response = requests.post(url, headers=self.headers, json=data)
        return response.json()

# Usage
client = OrderExportClient('https://api.example.com', 'your-token')

# Export specific orders
client.export_selective([1, 2, 3, 4, 5], 'my_orders.xlsx')

# Export with filters
filters = {
    'created_date_range': {
        'start_date': '2025-06-01',
        'end_date': '2025-06-20'
    },
    'status': ['completed'],
    'min_total_price': 100.0
}

# Preview first
preview = client.preview_export(filters)
print(f"Will export {preview['total_orders']} orders")

# Then export
client.export_bulk(filters, 'filtered_orders.xlsx', limit=1000)
```

## Error Handling

### Common Error Responses

```json
{
    "success": false,
    "error_type": "validation_error",
    "error_code": "INVALID_ORDER_IDS",
    "message": "Some order IDs are invalid or not accessible",
    "details": {
        "invalid_ids": [999, 1000],
        "accessible_count": 3
    }
}
```

```json
{
    "success": false,
    "error_type": "not_found",
    "error_code": "NO_ORDERS_FOUND",
    "message": "No orders found matching the specified filters",
    "details": {
        "filters_applied": {...}
    }
}
```

## Performance Considerations

### File Size Limits
- **Recommended**: < 10,000 orders per export
- **Maximum**: 50,000 orders (may take longer)
- **Estimated size**: ~0.5KB per order

### Optimization Tips
1. **Use filters** to limit result sets
2. **Set reasonable limits** (1000-5000 orders)
3. **Preview first** for large exports
4. **Use date ranges** to avoid full table scans

## Security

### Permissions
- **Master/Manager**: Can export all orders in their office
- **Employee**: Can export orders assigned to them or created by them
- **Office Isolation**: Users can only export orders from their office

### Data Protection
- **Temporary files** are automatically cleaned up
- **Access logging** for audit trails
- **Rate limiting** to prevent abuse

---

*This Excel export feature provides a complete solution for generating template-compliant Excel files with Arabic headers and comprehensive filtering capabilities.*
