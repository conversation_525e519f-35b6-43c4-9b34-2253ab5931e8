#!/usr/bin/env python3
"""
Test script for Excel import functionality.
This script tests the complete Excel import pipeline without requiring Django setup.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_excel_parsing():
    """Test Excel parsing functionality."""
    print("=== Testing Excel Parsing ===")
    
    try:
        from orders.excel_parser_service import parse_excel_orders
        from orders.excel_import_schemas import ExcelImportConfig, ExcelColumnMapping
        
        # Configure for our specific Excel format
        column_mapping = ExcelColumnMapping(
            serial_number_col=0,
            representative_name_col=1,
            customer_name_col=2,
            customer_phone_col=3,
            code_col=4,
            price_col=5,
            address_col=6,
            company_col=7,
            net_amount_col=8,
            status_col=9,
            header_row=5,  # Row with actual headers
            data_start_row=6  # First data row
        )
        
        config = ExcelImportConfig(
            column_mapping=column_mapping,
            batch_size=50,
            max_errors=10,
            skip_empty_rows=True,
            require_customer_name=True,
            require_customer_phone=True,
            require_code=True
        )
        
        file_path = "8-2-2025 رين واى.xlsx تمانيات.xlsx"
        
        if not os.path.exists(file_path):
            print(f"❌ Test file not found: {file_path}")
            return False
        
        print(f"📁 Parsing file: {file_path}")
        
        # Parse the Excel file
        import_result, processed_orders = parse_excel_orders(file_path, config)
        
        print(f"📊 Parsing Results:")
        print(f"   Total rows processed: {import_result.total_rows_processed}")
        print(f"   Successful imports: {import_result.successful_imports}")
        print(f"   Failed imports: {import_result.failed_imports}")
        print(f"   Skipped rows: {import_result.skipped_rows}")
        print(f"   Success rate: {import_result.success_rate:.1f}%")
        
        if import_result.errors:
            print(f"\n⚠️  Errors ({len(import_result.errors)}):")
            for error in import_result.errors[:5]:  # Show first 5 errors
                print(f"   Row {error['row_number']}: {error['error_message']}")
            if len(import_result.errors) > 5:
                print(f"   ... and {len(import_result.errors) - 5} more errors")
        
        if import_result.warnings:
            print(f"\n⚠️  Warnings ({len(import_result.warnings)}):")
            for warning in import_result.warnings[:3]:  # Show first 3 warnings
                print(f"   Row {warning['row_number']}: {warning['warning_message']}")
        
        # Show sample processed orders
        if processed_orders:
            print(f"\n✅ Sample processed orders ({len(processed_orders)} total):")
            for i, order in enumerate(processed_orders[:3]):
                print(f"   Order {i+1}:")
                print(f"     Code: {order.code}")
                print(f"     Customer: {order.customer_name}")
                print(f"     Phone: {order.customer_phone}")
                print(f"     Price: {order.total_price}")
                print(f"     Company: {order.customer_company_name}")
                print(f"     Address: {order.customer_address}")
                print()
        
        return len(processed_orders) > 0
        
    except Exception as e:
        print(f"❌ Error during parsing test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_validation():
    """Test data validation with sample data."""
    print("\n=== Testing Data Validation ===")
    
    try:
        from orders.excel_import_schemas import ProcessedOrderData
        
        # Test valid data
        valid_data = {
            "code": "F8-001",
            "customer_name": "أحمد محمد",
            "customer_phone": "01234567890",
            "customer_address": "القاهرة",
            "total_price": "150.50",
            "customer_company_name": "شركة الاختبار"
        }
        
        try:
            order = ProcessedOrderData(**valid_data)
            print(f"✅ Valid data test passed:")
            print(f"   Code: {order.code}")
            print(f"   Customer: {order.customer_name}")
            print(f"   Phone: {order.customer_phone}")
            print(f"   Price: {order.total_price}")
        except Exception as e:
            print(f"❌ Valid data test failed: {e}")
            return False
        
        # Test invalid data
        invalid_cases = [
            {"code": "", "customer_name": "Test", "customer_phone": "123"},  # Empty code
            {"code": "F8", "customer_name": "", "customer_phone": "123"},  # Empty name
            {"code": "F8", "customer_name": "Test", "customer_phone": ""},  # Empty phone
            {"code": "F8", "customer_name": "Test", "customer_phone": "123", "total_price": "invalid"},  # Invalid price
        ]
        
        print(f"\n🧪 Testing invalid data cases:")
        for i, invalid_data in enumerate(invalid_cases):
            try:
                ProcessedOrderData(**invalid_data)
                print(f"❌ Invalid case {i+1} should have failed but didn't")
                return False
            except Exception as e:
                print(f"✅ Invalid case {i+1} correctly failed: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during validation test: {e}")
        return False


def test_phone_number_cleaning():
    """Test phone number cleaning functionality."""
    print("\n=== Testing Phone Number Cleaning ===")
    
    try:
        from orders.excel_import_schemas import ProcessedOrderData
        
        test_cases = [
            ("01234567890", "01234567890"),  # Normal case
            ("01234567890\n01987654321", "01234567890"),  # Multiple phones
            ("01234567890-01987654321", "01234567890"),  # Dash separated
            ("+201234567890", "+201234567890"),  # With country code
            ("  01234567890  ", "01234567890"),  # With spaces
        ]
        
        print("📱 Testing phone number cleaning:")
        for input_phone, expected in test_cases:
            try:
                order = ProcessedOrderData(
                    code="TEST",
                    customer_name="Test User",
                    customer_phone=input_phone
                )
                result = order.customer_phone
                if result == expected:
                    print(f"✅ '{input_phone}' → '{result}'")
                else:
                    print(f"❌ '{input_phone}' → '{result}' (expected '{expected}')")
                    return False
            except Exception as e:
                print(f"❌ Phone '{input_phone}' failed: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during phone cleaning test: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Excel Import Tests\n")
    
    tests = [
        ("Excel Parsing", test_excel_parsing),
        ("Data Validation", test_data_validation),
        ("Phone Number Cleaning", test_phone_number_cleaning),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'='*50}")
    
    # Summary
    print(f"\n🏁 Test Results Summary:")
    print(f"{'='*50}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! The Excel import functionality is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
