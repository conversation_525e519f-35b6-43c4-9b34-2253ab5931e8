"""
Bulk order import service for handling database operations.
Manages bulk insertion of validated order data with proper error handling.
"""

from django.db import transaction, IntegrityError
from django.core.exceptions import ValidationError
from typing import List, Dict, Any, Optional, Tuple
import logging
from decimal import Decimal

from .models import Order, Company, OrderDeliveryStatus, OrderHandlingStatus
from .excel_import_schemas import ProcessedOrderData, ExcelImportResult, ExcelImportRequest
from accounts.models import User
from offices.models import Office

logger = logging.getLogger(__name__)


class BulkOrderImportService:
    """Service for bulk importing orders into the database."""
    
    def __init__(self, user: User, office: Office):
        """
        Initialize the bulk import service.
        
        Args:
            user: User performing the import
            office: Office context for the import
        """
        self.user = user
        self.office = office
        self.import_errors: List[Dict[str, Any]] = []
        self.import_warnings: List[Dict[str, Any]] = []
        
    def bulk_import_orders(
        self, 
        processed_orders: List[ProcessedOrderData],
        import_request: ExcelImportRequest
    ) -> ExcelImportResult:
        """
        Bulk import processed orders into the database.
        
        Args:
            processed_orders: List of validated order data
            import_request: Import configuration and settings
            
        Returns:
            ExcelImportResult with import statistics
        """
        imported_order_ids = []
        successful_imports = 0
        failed_imports = 0
        
        # Process in batches to avoid memory issues
        batch_size = 100
        
        try:
            with transaction.atomic():
                # Pre-load companies for faster lookup
                company_cache = self._build_company_cache()
                
                for i in range(0, len(processed_orders), batch_size):
                    batch = processed_orders[i:i + batch_size]
                    
                    batch_results = self._process_batch(
                        batch, 
                        import_request, 
                        company_cache,
                        start_index=i
                    )
                    
                    imported_order_ids.extend(batch_results['imported_ids'])
                    successful_imports += batch_results['successful']
                    failed_imports += batch_results['failed']
                    
                    logger.info(f"Processed batch {i//batch_size + 1}: "
                              f"{batch_results['successful']} successful, "
                              f"{batch_results['failed']} failed")
                
        except Exception as e:
            logger.error(f"Critical error during bulk import: {str(e)}")
            # Re-raise to trigger rollback
            raise
        
        # Create final result
        result = ExcelImportResult(
            total_rows_processed=len(processed_orders),
            successful_imports=successful_imports,
            failed_imports=failed_imports,
            skipped_rows=0,
            errors=self.import_errors,
            warnings=self.import_warnings,
            imported_order_ids=imported_order_ids
        )
        
        logger.info(f"Bulk import completed: {successful_imports} successful, "
                   f"{failed_imports} failed out of {len(processed_orders)} total")
        
        return result
    
    def _process_batch(
        self, 
        batch: List[ProcessedOrderData], 
        import_request: ExcelImportRequest,
        company_cache: Dict[str, Company],
        start_index: int = 0
    ) -> Dict[str, Any]:
        """
        Process a batch of orders.
        
        Args:
            batch: Batch of orders to process
            import_request: Import configuration
            company_cache: Pre-loaded company cache
            start_index: Starting index for error reporting
            
        Returns:
            Dictionary with batch processing results
        """
        imported_ids = []
        successful = 0
        failed = 0
        
        for idx, order_data in enumerate(batch):
            row_number = start_index + idx + 1
            
            try:
                # Check for duplicates if required
                if import_request.skip_duplicates:
                    if self._is_duplicate_order(order_data):
                        self._add_warning(
                            row_number, 
                            "duplicate_order", 
                            f"Order with code '{order_data.code}' already exists - skipped"
                        )
                        continue
                
                # Create order
                order = self._create_order(order_data, import_request, company_cache, row_number)
                
                if order:
                    imported_ids.append(order.id)
                    successful += 1
                else:
                    failed += 1
                    
            except Exception as e:
                self._add_error(
                    row_number, 
                    "database_error", 
                    f"Failed to create order: {str(e)}"
                )
                failed += 1
        
        return {
            'imported_ids': imported_ids,
            'successful': successful,
            'failed': failed
        }
    
    def _create_order(
        self, 
        order_data: ProcessedOrderData, 
        import_request: ExcelImportRequest,
        company_cache: Dict[str, Company],
        row_number: int
    ) -> Optional[Order]:
        """
        Create a single order in the database.
        
        Args:
            order_data: Processed order data
            import_request: Import configuration
            company_cache: Company cache for lookup
            row_number: Row number for error reporting
            
        Returns:
            Created Order instance or None if failed
        """
        try:
            # Resolve customer company
            customer_company = None
            if order_data.customer_company_name:
                customer_company = self._resolve_company(
                    order_data.customer_company_name,
                    company_cache,
                    import_request,
                    row_number
                )
            elif import_request.default_company_id:
                try:
                    customer_company = Company.objects.get(
                        id=import_request.default_company_id,
                        office=self.office
                    )
                except Company.DoesNotExist:
                    self._add_warning(
                        row_number,
                        "company_not_found",
                        f"Default company ID {import_request.default_company_id} not found"
                    )
            
            # Create order
            order = Order.objects.create(
                office=self.office,
                code=order_data.code,
                customer_name=order_data.customer_name,
                customer_phone=order_data.customer_phone,
                customer_address=order_data.customer_address or "",
                total_price=order_data.total_price,
                customer_company=customer_company,
                notes=order_data.notes or f"Imported from Excel - Row {row_number}",
                order_handling_status=OrderHandlingStatus.PENDING,
                # Set other fields as needed
                breakable=False,  # Default value
            )
            
            logger.debug(f"Created order {order.id} for customer {order.customer_name}")
            return order
            
        except IntegrityError as e:
            self._add_error(
                row_number,
                "integrity_error",
                f"Database integrity error: {str(e)}"
            )
            return None
        except ValidationError as e:
            self._add_error(
                row_number,
                "validation_error",
                f"Django validation error: {str(e)}"
            )
            return None
        except Exception as e:
            self._add_error(
                row_number,
                "unexpected_error",
                f"Unexpected error creating order: {str(e)}"
            )
            return None
    
    def _resolve_company(
        self, 
        company_name: str, 
        company_cache: Dict[str, Company],
        import_request: ExcelImportRequest,
        row_number: int
    ) -> Optional[Company]:
        """
        Resolve company by name, optionally creating if not found.
        
        Args:
            company_name: Name of the company
            company_cache: Company cache
            import_request: Import configuration
            row_number: Row number for error reporting
            
        Returns:
            Company instance or None
        """
        # Normalize company name for lookup
        normalized_name = company_name.strip().lower()
        
        # Check cache first
        if normalized_name in company_cache:
            return company_cache[normalized_name]
        
        # Try database lookup
        try:
            company = Company.objects.get(
                name__iexact=company_name,
                office=self.office,
                is_active=True
            )
            # Add to cache
            company_cache[normalized_name] = company
            return company
            
        except Company.DoesNotExist:
            if import_request.create_missing_companies:
                # Create new company
                try:
                    company = Company.objects.create(
                        office=self.office,
                        name=company_name,
                        is_active=True
                    )
                    company_cache[normalized_name] = company
                    self._add_warning(
                        row_number,
                        "company_created",
                        f"Created new company: {company_name}"
                    )
                    return company
                except Exception as e:
                    self._add_error(
                        row_number,
                        "company_creation_error",
                        f"Failed to create company '{company_name}': {str(e)}"
                    )
                    return None
            else:
                self._add_warning(
                    row_number,
                    "company_not_found",
                    f"Company '{company_name}' not found and creation disabled"
                )
                return None
        
        except Company.MultipleObjectsReturned:
            self._add_warning(
                row_number,
                "multiple_companies",
                f"Multiple companies found with name '{company_name}' - using first match"
            )
            company = Company.objects.filter(
                name__iexact=company_name,
                office=self.office,
                is_active=True
            ).first()
            if company:
                company_cache[normalized_name] = company
            return company
    
    def _build_company_cache(self) -> Dict[str, Company]:
        """
        Build a cache of companies for faster lookup.
        
        Returns:
            Dictionary mapping normalized company names to Company instances
        """
        companies = Company.objects.filter(office=self.office, is_active=True)
        cache = {}
        
        for company in companies:
            normalized_name = company.name.strip().lower()
            cache[normalized_name] = company
            
        logger.info(f"Built company cache with {len(cache)} companies")
        return cache
    
    def _is_duplicate_order(self, order_data: ProcessedOrderData) -> bool:
        """
        Check if an order with the same code already exists.
        
        Args:
            order_data: Order data to check
            
        Returns:
            True if duplicate exists
        """
        return Order.objects.filter(
            office=self.office,
            code=order_data.code
        ).exists()
    
    def _add_error(self, row_number: int, error_type: str, message: str):
        """Add an error to the import errors list."""
        error = {
            'row_number': row_number,
            'error_type': error_type,
            'error_message': message
        }
        self.import_errors.append(error)
        logger.warning(f"Import error - Row {row_number} ({error_type}): {message}")
    
    def _add_warning(self, row_number: int, warning_type: str, message: str):
        """Add a warning to the import warnings list."""
        warning = {
            'row_number': row_number,
            'warning_type': warning_type,
            'warning_message': message
        }
        self.import_warnings.append(warning)
        logger.info(f"Import warning - Row {row_number} ({warning_type}): {message}")


def bulk_import_orders(
    processed_orders: List[ProcessedOrderData],
    import_request: ExcelImportRequest,
    user: User,
    office: Office
) -> ExcelImportResult:
    """
    Convenience function for bulk importing orders.
    
    Args:
        processed_orders: List of processed order data
        import_request: Import configuration
        user: User performing the import
        office: Office context
        
    Returns:
        ExcelImportResult with import statistics
    """
    service = BulkOrderImportService(user, office)
    return service.bulk_import_orders(processed_orders, import_request)
