"""
Intelligent column mapping system for Excel import.
Automatically detects and maps column names to appropriate database fields.
"""

import pandas as pd
import re
from typing import Dict, List, Optional, Tuple, Any
import logging
from difflib import SequenceMatcher

logger = logging.getLogger(__name__)


class IntelligentColumnMapper:
    """Intelligent column mapper that detects column names and maps them to database fields."""
    
    def __init__(self):
        """Initialize the mapper with predefined field patterns."""
        self.field_patterns = {
            # Customer name patterns
            'customer_name': [
                'الاسم', 'اسم', 'name', 'customer_name', 'customer', 'عميل', 'زبون',
                'اسم العميل', 'اسم الزبون', 'الزبون', 'العميل'
            ],
            
            # Phone number patterns
            'customer_phone': [
                'رقم التليفون', 'التليفون', 'تليفون', 'هاتف', 'موبايل', 'phone', 'mobile',
                'telephone', 'رقم الهاتف', 'رقم الموبايل', 'رقم', 'contact'
            ],
            
            # Company code patterns
            'company_code': [
                'رمز الشركة', 'رمز', 'كود', 'code', 'company_code', 'comp_code',
                'شركة_رمز', 'كود_الشركة', 'رقم_الشركة'
            ],
            
            # Company name patterns
            'company_name': [
                'الشركة', 'شركة', 'company', 'company_name', 'اسم الشركة',
                'مؤسسة', 'جهة', 'organization'
            ],
            
            # Price/amount patterns
            'total_price': [
                'قيمة الاوردر', 'قيمة', 'سعر', 'مبلغ', 'إجمالي', 'price', 'amount',
                'total', 'cost', 'value', 'order_value', 'المبلغ', 'السعر'
            ],
            
            # Address patterns
            'customer_address': [
                'العنوان', 'عنوان', 'address', 'location', 'مكان', 'المكان',
                'المنطقة', 'منطقة', 'المحافظة', 'المركز', 'area'
            ],
            
            # Serial number patterns
            'serial_number': [
                'م', 'رقم', 'serial', 'number', 'id', 'index', 'ترقيم',
                'رقم_تسلسلي', 'تسلسل'
            ],
            
            # Representative patterns
            'representative_name': [
                'المندوب', 'مندوب', 'representative', 'rep', 'اسم المندوب',
                'حساب المندوب', 'مندوب المبيعات'
            ],
            
            # Status patterns
            'status': [
                'الحالة', 'حالة', 'status', 'state', 'وضع', 'الوضع'
            ],
            
            # Notes patterns
            'notes': [
                'ملاحظات', 'ملاحظة', 'notes', 'note', 'تعليق', 'تعليقات',
                'وصف', 'description', 'remarks'
            ]
        }
        
    def detect_header_row(self, df: pd.DataFrame) -> Optional[int]:
        """
        Detect which row contains the column headers.
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            Row index of headers, or None if not found
        """
        for i in range(min(15, len(df))):  # Check first 15 rows
            row = df.iloc[i]
            
            # Skip completely empty rows
            if row.isnull().all():
                continue
                
            # Count text values that might be headers
            text_count = 0
            total_non_null = row.notna().sum()
            
            for val in row:
                if pd.notna(val) and isinstance(val, str):
                    val_clean = str(val).strip()
                    if val_clean and self._looks_like_header(val_clean):
                        text_count += 1
            
            # If we have multiple text values that look like headers
            if text_count >= 3 and text_count >= total_non_null * 0.6:
                logger.info(f"Detected header row at index {i}")
                return i
                
        return None
    
    def _looks_like_header(self, text: str) -> bool:
        """
        Check if a text value looks like a column header.
        
        Args:
            text: Text to check
            
        Returns:
            True if it looks like a header
        """
        text_clean = text.strip().lower()
        
        # Check against known patterns
        for field_patterns in self.field_patterns.values():
            for pattern in field_patterns:
                if pattern.lower() in text_clean or text_clean in pattern.lower():
                    return True
        
        # Check for common header characteristics
        if len(text_clean) > 2 and len(text_clean) < 50:
            # Contains Arabic or English letters
            if re.search(r'[\u0600-\u06FF]', text) or re.search(r'[a-zA-Z]', text):
                return True
                
        return False
    
    def map_columns(self, df: pd.DataFrame, header_row: int) -> Dict[str, int]:
        """
        Map detected column headers to database fields.
        
        Args:
            df: DataFrame with data
            header_row: Row index containing headers
            
        Returns:
            Dictionary mapping field names to column indices
        """
        headers = df.iloc[header_row].tolist()
        column_mapping = {}
        
        logger.info(f"Mapping columns from headers: {headers}")
        
        for col_idx, header in enumerate(headers):
            if pd.isna(header):
                continue
                
            header_clean = str(header).strip()
            if not header_clean:
                continue
                
            # Find best matching field
            best_field = self._find_best_field_match(header_clean)
            if best_field:
                column_mapping[best_field] = col_idx
                logger.info(f"Mapped column {col_idx} '{header_clean}' → {best_field}")
        
        return column_mapping
    
    def _find_best_field_match(self, header: str) -> Optional[str]:
        """
        Find the best matching field for a header.
        
        Args:
            header: Column header text
            
        Returns:
            Best matching field name or None
        """
        header_clean = header.strip().lower()
        best_field = None
        best_score = 0
        
        for field, patterns in self.field_patterns.items():
            for pattern in patterns:
                pattern_clean = pattern.lower()
                
                # Exact match
                if header_clean == pattern_clean:
                    return field
                
                # Substring match
                if pattern_clean in header_clean or header_clean in pattern_clean:
                    score = len(pattern_clean) / max(len(header_clean), len(pattern_clean))
                    if score > best_score:
                        best_score = score
                        best_field = field
                
                # Similarity match
                similarity = SequenceMatcher(None, header_clean, pattern_clean).ratio()
                if similarity > 0.8 and similarity > best_score:
                    best_score = similarity
                    best_field = field
        
        # Only return if we have a good confidence
        if best_score > 0.6:
            return best_field
            
        return None
    
    def create_excel_column_mapping(self, file_path: str) -> Tuple[Dict[str, int], int]:
        """
        Create column mapping for an Excel file.
        
        Args:
            file_path: Path to Excel file
            
        Returns:
            Tuple of (column_mapping, data_start_row)
        """
        try:
            # Read Excel file
            df = pd.read_excel(file_path, header=None)
            
            # Detect header row
            header_row = self.detect_header_row(df)
            if header_row is None:
                raise ValueError("Could not detect header row in Excel file")
            
            # Map columns
            column_mapping = self.map_columns(df, header_row)
            
            # Data starts after header row
            data_start_row = header_row + 1
            
            logger.info(f"Created mapping: {column_mapping}")
            logger.info(f"Data starts at row: {data_start_row}")
            
            return column_mapping, data_start_row
            
        except Exception as e:
            logger.error(f"Error creating column mapping: {str(e)}")
            raise
    
    def validate_mapping(self, column_mapping: Dict[str, int], required_fields: List[str] = None) -> Dict[str, Any]:
        """
        Validate the column mapping.
        
        Args:
            column_mapping: Column mapping to validate
            required_fields: List of required field names
            
        Returns:
            Validation result with status and details
        """
        if required_fields is None:
            required_fields = ['customer_name', 'customer_phone']
        
        result = {
            'valid': True,
            'missing_required': [],
            'mapped_fields': list(column_mapping.keys()),
            'warnings': []
        }
        
        # Check required fields
        for field in required_fields:
            if field not in column_mapping:
                result['missing_required'].append(field)
                result['valid'] = False
        
        # Check for important optional fields
        important_optional = ['company_code', 'total_price', 'customer_address']
        for field in important_optional:
            if field not in column_mapping:
                result['warnings'].append(f"Optional field '{field}' not found")
        
        return result
    
    def get_mapping_summary(self, column_mapping: Dict[str, int], df: pd.DataFrame, header_row: int) -> str:
        """
        Get a human-readable summary of the column mapping.
        
        Args:
            column_mapping: Column mapping
            df: DataFrame
            header_row: Header row index
            
        Returns:
            Formatted summary string
        """
        headers = df.iloc[header_row].tolist()
        summary_lines = ["Column Mapping Summary:"]
        summary_lines.append("=" * 40)
        
        for field, col_idx in column_mapping.items():
            header_name = headers[col_idx] if col_idx < len(headers) else "Unknown"
            summary_lines.append(f"{field:20} ← Column {col_idx}: '{header_name}'")
        
        # Show unmapped columns
        mapped_indices = set(column_mapping.values())
        unmapped = []
        for i, header in enumerate(headers):
            if i not in mapped_indices and pd.notna(header) and str(header).strip():
                unmapped.append(f"Column {i}: '{header}'")
        
        if unmapped:
            summary_lines.append("\nUnmapped columns:")
            summary_lines.extend(unmapped)
        
        return "\n".join(summary_lines)


def create_intelligent_mapping(file_path: str) -> Tuple[Dict[str, int], int, Dict[str, Any]]:
    """
    Create intelligent column mapping for an Excel file.
    
    Args:
        file_path: Path to Excel file
        
    Returns:
        Tuple of (column_mapping, data_start_row, validation_result)
    """
    mapper = IntelligentColumnMapper()
    column_mapping, data_start_row = mapper.create_excel_column_mapping(file_path)
    validation_result = mapper.validate_mapping(column_mapping)
    
    return column_mapping, data_start_row, validation_result
