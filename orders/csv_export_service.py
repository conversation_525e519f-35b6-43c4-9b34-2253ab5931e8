"""
CSV export service for orders.
Provides simple CSV export functionality as an alternative to Excel export.
"""

import csv
import tempfile
import os
from datetime import date
from typing import Optional
from django.db.models import QuerySet
from django.http import HttpResponse


def export_orders_to_csv(
    orders: QuerySet,
    filename: Optional[str] = None,
    include_bom: bool = True
) -> str:
    """
    Export orders to CSV file.
    
    Args:
        orders: QuerySet of Order objects to export
        filename: Custom filename (optional)
        include_bom: Include BOM for proper UTF-8 encoding in Excel (default: True)
    
    Returns:
        Path to the generated CSV file
    """
    # Generate filename if not provided
    if not filename:
        filename = f"orders_export_{date.today().strftime('%Y%m%d')}.csv"
    
    if not filename.endswith('.csv'):
        filename += '.csv'
    
    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig' if include_bom else 'utf-8')
    
    try:
        writer = csv.writer(temp_file, quoting=csv.QUOTE_ALL)
        
        # Write headers (in English for simplicity)
        headers = [
            'Order ID',
            'Order Code', 
            'Customer Name',
            'Customer Phone',
            'Customer Address',
            'Company',
            'Total Price',
            'Status',
            'Assigned To',
            'Created Date',
            'Updated Date',
            'Notes'
        ]
        writer.writerow(headers)
        
        # Write data rows
        for order in orders:
            row = [
                order.id,
                order.code,
                order.customer_name,
                order.customer_phone,
                order.customer_address or '',
                order.customer_company.name if order.customer_company else '',
                float(order.total_price) if order.total_price else 0,
                order.order_handling_status,
                order.assigned_to.get_full_name() if order.assigned_to else '',
                order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                order.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                order.notes or ''
            ]
            writer.writerow(row)
        
        temp_file.close()
        return temp_file.name
        
    except Exception as e:
        temp_file.close()
        # Clean up file on error
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)
        raise e


def export_orders_to_csv_response(
    orders: QuerySet,
    filename: Optional[str] = None,
    include_bom: bool = True
) -> HttpResponse:
    """
    Export orders to CSV and return as HTTP response.
    
    Args:
        orders: QuerySet of Order objects to export
        filename: Custom filename (optional)
        include_bom: Include BOM for proper UTF-8 encoding in Excel (default: True)
    
    Returns:
        HttpResponse with CSV content
    """
    # Generate filename if not provided
    if not filename:
        filename = f"orders_export_{date.today().strftime('%Y%m%d')}.csv"
    
    if not filename.endswith('.csv'):
        filename += '.csv'
    
    # Create HTTP response with CSV content type
    response = HttpResponse(content_type='text/csv; charset=utf-8-sig' if include_bom else 'text/csv; charset=utf-8')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    
    # Add BOM for proper UTF-8 encoding in Excel
    if include_bom:
        response.write('\ufeff')
    
    writer = csv.writer(response, quoting=csv.QUOTE_ALL)
    
    # Write headers (in English for simplicity)
    headers = [
        'Order ID',
        'Order Code', 
        'Customer Name',
        'Customer Phone',
        'Customer Address',
        'Company',
        'Total Price',
        'Status',
        'Assigned To',
        'Created Date',
        'Updated Date',
        'Notes'
    ]
    writer.writerow(headers)
    
    # Write data rows
    for order in orders:
        row = [
            order.id,
            order.code,
            order.customer_name,
            order.customer_phone,
            order.customer_address or '',
            order.customer_company.name if order.customer_company else '',
            float(order.total_price) if order.total_price else 0,
            order.order_handling_status,
            order.assigned_to.get_full_name() if order.assigned_to else '',
            order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            order.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
            order.notes or ''
        ]
        writer.writerow(row)
    
    return response


def export_orders_to_csv_arabic(
    orders: QuerySet,
    filename: Optional[str] = None,
    include_bom: bool = True
) -> HttpResponse:
    """
    Export orders to CSV with Arabic headers.
    
    Args:
        orders: QuerySet of Order objects to export
        filename: Custom filename (optional)
        include_bom: Include BOM for proper UTF-8 encoding in Excel (default: True)
    
    Returns:
        HttpResponse with CSV content
    """
    # Generate filename if not provided
    if not filename:
        filename = f"orders_export_{date.today().strftime('%Y%m%d')}.csv"
    
    if not filename.endswith('.csv'):
        filename += '.csv'
    
    # Create HTTP response with CSV content type
    response = HttpResponse(content_type='text/csv; charset=utf-8-sig' if include_bom else 'text/csv; charset=utf-8')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    
    # Add BOM for proper UTF-8 encoding in Excel
    if include_bom:
        response.write('\ufeff')
    
    writer = csv.writer(response, quoting=csv.QUOTE_ALL)
    
    # Write headers in Arabic
    headers = [
        'رقم الطلب',
        'كود الطلب', 
        'اسم العميل',
        'رقم الهاتف',
        'العنوان',
        'الشركة',
        'السعر الإجمالي',
        'الحالة',
        'مُعين إلى',
        'تاريخ الإنشاء',
        'تاريخ التحديث',
        'ملاحظات'
    ]
    writer.writerow(headers)
    
    # Write data rows
    for order in orders:
        row = [
            order.id,
            order.code,
            order.customer_name,
            order.customer_phone,
            order.customer_address or '',
            order.customer_company.name if order.customer_company else '',
            float(order.total_price) if order.total_price else 0,
            order.order_handling_status,
            order.assigned_to.get_full_name() if order.assigned_to else '',
            order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            order.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
            order.notes or ''
        ]
        writer.writerow(row)
    
    return response
