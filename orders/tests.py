"""
Unit tests for Excel import functionality.
"""

import unittest
from decimal import Decimal
from unittest.mock import patch, MagicMock
import tempfile
import os
from django.test import TestCase

from .excel_import_schemas import (
    ExcelRowData,
    ProcessedOrderData,
    ExcelImportConfig,
    ExcelColumnMapping,
    ExcelImportResult,
)
from .excel_parser_service import ExcelParserService


class TestExcelImportSchemas(TestCase):
    """Test Excel import schemas and validation."""

    def test_excel_row_data_validation(self):
        """Test ExcelRowData validation with various input types."""
        # Test with mixed data types (as would come from Excel)
        data = {
            "serial_number": 1,
            "representative_name": "أحمد",
            "customer_name": "محمد علي",
            "customer_phone": 1234567890,  # Integer phone number
            "code": "F8",
            "price": 150.50,
            "address": "القاهرة",
            "company": "شركة الاختبار",
            "net_amount": 140.0,
            "status": "جديد",
        }

        row = ExcelRowData(**data)
        self.assertEqual(row.customer_phone, 1234567890)
        self.assertEqual(row.customer_name, "محمد علي")
        self.assertEqual(row.price, 150.50)

    def test_processed_order_data_validation(self):
        """Test ProcessedOrderData validation and conversion."""
        data = {
            "code": "F8-001",
            "customer_name": "أحمد محمد",
            "customer_phone": "01234567890",
            "customer_address": "القاهرة",
            "total_price": "150.50",
            "customer_company_name": "شركة الاختبار",
        }

        order = ProcessedOrderData(**data)
        self.assertEqual(order.code, "F8-001")
        self.assertEqual(order.customer_name, "أحمد محمد")
        self.assertEqual(order.customer_phone, "01234567890")
        self.assertEqual(order.total_price, Decimal("150.50"))

    def test_phone_number_validation(self):
        """Test phone number validation and cleaning."""
        # Test valid phone numbers
        valid_phones = [
            ("01234567890", "01234567890"),
            ("01234567890\n01987654321", "01234567890"),  # Multiple phones
            ("01234567890-01987654321", "01234567890"),  # Dash separated
            ("+201234567890", "+201234567890"),  # With country code
            ("  01234567890  ", "01234567890"),  # With spaces
        ]

        for input_phone, expected in valid_phones:
            with self.subTest(phone=input_phone):
                order = ProcessedOrderData(
                    code="TEST", customer_name="Test User", customer_phone=input_phone
                )
                self.assertEqual(order.customer_phone, expected)

    def test_phone_number_validation_errors(self):
        """Test phone number validation errors."""
        invalid_phones = ["", "123", "12345"]  # Too short

        for phone in invalid_phones:
            with self.subTest(phone=phone):
                with self.assertRaises(ValueError):
                    ProcessedOrderData(
                        code="TEST", customer_name="Test User", customer_phone=phone
                    )

    def test_price_validation(self):
        """Test price validation and conversion."""
        # Test valid prices
        valid_prices = [
            ("150.50", Decimal("150.50")),
            ("150", Decimal("150")),
            (150.50, Decimal("150.50")),
            (150, Decimal("150")),
            ("", None),
            (None, None),
        ]

        for input_price, expected in valid_prices:
            with self.subTest(price=input_price):
                order = ProcessedOrderData(
                    code="TEST",
                    customer_name="Test User",
                    customer_phone="01234567890",
                    total_price=input_price,
                )
                self.assertEqual(order.total_price, expected)

    def test_required_fields_validation(self):
        """Test that required fields are validated."""
        # Missing code
        with self.assertRaises(ValueError):
            ProcessedOrderData(customer_name="Test User", customer_phone="01234567890")

        # Missing customer_name
        with self.assertRaises(ValueError):
            ProcessedOrderData(code="TEST", customer_phone="01234567890")

        # Missing customer_phone
        with self.assertRaises(ValueError):
            ProcessedOrderData(code="TEST", customer_name="Test User")


class TestExcelImportResult(TestCase):
    """Test Excel import result calculations."""

    def test_success_rate_calculation(self):
        """Test success rate calculation."""
        # Test normal case
        result = ExcelImportResult(
            total_rows_processed=100, successful_imports=80, failed_imports=20
        )
        self.assertEqual(result.success_rate, 80.0)

        # Test zero division
        result_empty = ExcelImportResult(
            total_rows_processed=0, successful_imports=0, failed_imports=0
        )
        self.assertEqual(result_empty.success_rate, 0.0)

        # Test perfect success
        result_perfect = ExcelImportResult(
            total_rows_processed=50, successful_imports=50, failed_imports=0
        )
        self.assertEqual(result_perfect.success_rate, 100.0)
