"""
Excel Export Service for Orders
Generates .xlsx files matching the exact template format with Arabic headers.
"""

import os
import tempfile
from datetime import datetime, date
from decimal import Decimal
from typing import List, Optional, Dict, Any
from pathlib import Path

import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter
from django.conf import settings
from django.db.models import QuerySet

import logging

logger = logging.getLogger(__name__)


class OrderExcelExporter:
    """Service for exporting orders to Excel format matching the template."""

    def __init__(self, template_path: Optional[str] = None):
        """
        Initialize the exporter.

        Args:
            template_path: Path to the template Excel file
        """
        self.template_path = template_path or self._get_default_template_path()
        self.workbook = None
        self.worksheet = None

    def _get_default_template_path(self) -> str:
        """Get the default template file path."""
        # Try to find the template in the project root
        possible_paths = [
            "نسخة مناديب اورجينال - Copy.xlsx",
            os.path.join(settings.BASE_DIR, "نسخة مناديب اورجينال - Copy.xlsx"),
            os.path.join(
                settings.BASE_DIR, "templates", "excel", "order_template.xlsx"
            ),
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

        # If no template found, we'll create from scratch
        return None

    def create_workbook_from_template(self) -> openpyxl.Workbook:
        """
        Create a new workbook based on the template structure.

        Returns:
            New workbook with template formatting
        """
        if self.template_path and os.path.exists(self.template_path):
            # Load existing template
            try:
                wb = openpyxl.load_workbook(self.template_path)
                # Clear existing data but keep formatting
                ws = wb.active

                # Clear data rows (keep headers and structure)
                for row in range(7, ws.max_row + 1):
                    for col in range(1, 9):
                        cell = ws.cell(row=row, column=col)
                        cell.value = None

                return wb
            except Exception as e:
                logger.warning(f"Could not load template {self.template_path}: {e}")

        # Create workbook from scratch with template structure
        return self._create_template_workbook()

    def _create_template_workbook(self) -> openpyxl.Workbook:
        """Create a new workbook with the template structure."""
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "1"  # Match template sheet name

        # Set up fonts
        header_font = Font(name="Times New Roman", bold=True, size=12)
        data_font = Font(name="Times New Roman", size=11)

        # Set up alignment
        center_alignment = Alignment(horizontal="center", vertical="center")
        right_alignment = Alignment(horizontal="right", vertical="center")

        # Representative name (Row 3, Column B)
        ws["B3"] = ""  # Will be filled with representative name
        ws["B3"].font = header_font
        ws["B3"].alignment = right_alignment

        # Date label (Row 4, Column B)
        ws["B4"] = "اسم المندوب"
        ws["B4"].font = header_font
        ws["B4"].alignment = right_alignment

        # Date value (Row 5, Column B)
        ws["B5"] = "التــــــــاريـــــــــــــــــــــــــــخ"
        ws["B5"].font = header_font
        ws["B5"].alignment = center_alignment

        # Headers (Row 6)
        headers = [
            "",
            "م",
            " الاسم",
            "التليفون",
            "الشركه",
            "السعر",
            "الحاله",
            "حالة أولي",
        ]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=6, column=col)
            cell.value = header
            cell.font = header_font
            cell.alignment = center_alignment

        # Set column widths to match template
        column_widths = [3, 5, 20, 15, 10, 10, 20, 12]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(col)].width = width

        return wb

    def export_orders(
        self,
        orders: QuerySet,
        representative_name: str,
        export_date: Optional[date] = None,
        filename: Optional[str] = None,
    ) -> str:
        """
        Export orders to Excel file.

        Args:
            orders: QuerySet of Order objects to export
            representative_name: Name to display as representative
            export_date: Date to display in the file
            filename: Custom filename (optional)

        Returns:
            Path to the generated Excel file
        """
        if export_date is None:
            export_date = date.today()

        # Create workbook
        self.workbook = self.create_workbook_from_template()
        self.worksheet = self.workbook.active

        # Set representative name and date
        self._set_header_info(representative_name, export_date)

        # Export order data
        self._export_order_data(orders)

        # Generate filename
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"orders_export_{timestamp}.xlsx"

        # Save to temporary file
        temp_dir = tempfile.gettempdir()
        file_path = os.path.join(temp_dir, filename)

        self.workbook.save(file_path)
        logger.info(f"Exported {orders.count()} orders to {file_path}")

        return file_path

    def _set_header_info(self, representative_name: str, export_date: date):
        """Set the representative name and date in the header."""
        # Set representative name (Row 3, Column B)
        self.worksheet["B3"] = representative_name

        # Set date (Row 5, Column B) - keeping the Arabic date label
        # The template has the date label in B5, so we'll put actual date in C5 or modify B5
        date_str = export_date.strftime("%Y/%m/%d")
        self.worksheet["B5"] = f"التــــــــاريـــــــــــــــــــــــــــخ: {date_str}"

    def _export_order_data(self, orders: QuerySet):
        """Export order data to the worksheet."""
        data_font = Font(name="Times New Roman", size=11)
        center_alignment = Alignment(horizontal="center", vertical="center")
        right_alignment = Alignment(horizontal="right", vertical="center")

        # Start from row 7 (after headers)
        current_row = 7

        for index, order in enumerate(orders, 1):
            # Column A: Empty (space)
            self.worksheet.cell(row=current_row, column=1).value = (
                " " if index == 1 else None
            )

            # Column B: Serial number (م)
            serial_cell = self.worksheet.cell(row=current_row, column=2)
            if index == 1:
                serial_cell.value = 1
            else:
                # Use Excel formula for auto-increment
                prev_row = current_row - 1
                serial_cell.value = f"=B{prev_row}+1"
            serial_cell.font = data_font
            serial_cell.alignment = center_alignment

            # Column C: Customer name (الاسم)
            name_cell = self.worksheet.cell(row=current_row, column=3)
            name_cell.value = order.customer_name
            name_cell.font = data_font
            name_cell.alignment = right_alignment

            # Column D: Phone number (التليفون)
            phone_cell = self.worksheet.cell(row=current_row, column=4)
            phone_cell.value = order.customer_phone
            phone_cell.font = data_font
            phone_cell.alignment = center_alignment

            # Column E: Company (الشركه)
            company_cell = self.worksheet.cell(row=current_row, column=5)
            company_name = self._get_company_display_name(order)
            company_cell.value = company_name
            company_cell.font = data_font
            company_cell.alignment = center_alignment

            # Column F: Price (السعر)
            price_cell = self.worksheet.cell(row=current_row, column=6)
            price_cell.value = float(order.total_price) if order.total_price else 0
            price_cell.font = data_font
            price_cell.alignment = center_alignment

            # Column G: Status/Notes (الحاله)
            status_cell = self.worksheet.cell(row=current_row, column=7)
            status_cell.value = self._get_status_display(order)
            status_cell.font = data_font
            status_cell.alignment = right_alignment

            # Column H: Initial status/amount (حالة أولي)
            initial_cell = self.worksheet.cell(row=current_row, column=8)
            initial_cell.value = float(order.total_price) if order.total_price else 0
            initial_cell.font = data_font
            initial_cell.alignment = center_alignment

            current_row += 1

    def _get_company_display_name(self, order) -> str:
        """Get the company display name for the order."""
        if hasattr(order, "customer_company") and order.customer_company:
            # Use company code if available, otherwise company name
            if hasattr(order.customer_company, "code") and order.customer_company.code:
                return order.customer_company.code
            elif (
                hasattr(order.customer_company, "name") and order.customer_company.name
            ):
                return order.customer_company.name

        # Fallback to customer company name if available
        if hasattr(order, "customer_company_name") and order.customer_company_name:
            return order.customer_company_name

        return ""

    def _get_status_display(self, order) -> str:
        """Get the status display for the order."""
        status_parts = []

        # Add order status if available
        if hasattr(order, "order_handling_status") and order.order_handling_status:
            status_parts.append(str(order.order_handling_status))

        # Add notes if available
        if hasattr(order, "notes") and order.notes:
            status_parts.append(str(order.notes))

        # Add customer address if available and no other status
        if (
            not status_parts
            and hasattr(order, "customer_address")
            and order.customer_address
        ):
            status_parts.append(str(order.customer_address))

        return " - ".join(status_parts) if status_parts else ""


def export_orders_to_excel(
    orders: QuerySet,
    representative_name: str,
    export_date: Optional[date] = None,
    filename: Optional[str] = None,
    template_path: Optional[str] = None,
) -> str:
    """
    Convenience function to export orders to Excel.

    Args:
        orders: QuerySet of Order objects to export
        representative_name: Name to display as representative
        export_date: Date to display in the file
        filename: Custom filename (optional)
        template_path: Path to template file (optional)

    Returns:
        Path to the generated Excel file
    """
    exporter = OrderExcelExporter(template_path)
    return exporter.export_orders(orders, representative_name, export_date, filename)


def generate_representative_name(user, office=None) -> str:
    """
    Generate representative name by concatenating office name + user name.

    Args:
        user: User object
        office: Office object (optional, will try to get from user)

    Returns:
        Formatted representative name
    """
    if not office and hasattr(user, "office"):
        office = user.office

    office_name = (
        office.name if office and hasattr(office, "name") else "Unknown Office"
    )

    # Get user's full name or username
    if hasattr(user, "get_full_name") and user.get_full_name():
        user_name = user.get_full_name()
    elif hasattr(user, "first_name") and hasattr(user, "last_name"):
        user_name = f"{user.first_name} {user.last_name}".strip()
        if not user_name:
            user_name = user.username
    else:
        user_name = str(user)

    return f"{office_name} - {user_name}"
