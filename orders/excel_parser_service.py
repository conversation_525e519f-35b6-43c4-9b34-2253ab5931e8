"""
Excel parser service for processing order import files.
Handles Excel file parsing, data extraction, and validation.
"""

import pandas as pd
import openpyxl
from typing import List, Dict, Any, Optional, Union, Iterator
import logging
from decimal import Decimal
import re
from pathlib import Path

from .excel_import_schemas import (
    ExcelRowData,
    ProcessedOrderData,
    ExcelImportResult,
    ExcelImportError,
    ExcelImportWarning,
    ExcelColumnMapping,
    ExcelImportConfig,
)

logger = logging.getLogger(__name__)


class ExcelParserService:
    """Service for parsing Excel files and extracting order data."""

    def __init__(self, config: ExcelImportConfig = None):
        """
        Initialize the Excel parser service.

        Args:
            config: Configuration for the import process
        """
        self.config = config or ExcelImportConfig()
        self.errors: List[ExcelImportError] = []
        self.warnings: List[ExcelImportWarning] = []

    def parse_excel_file(self, file_path: Union[str, Path]) -> ExcelImportResult:
        """
        Parse an Excel file and extract order data.

        Args:
            file_path: Path to the Excel file

        Returns:
            ExcelImportResult with parsing results
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"Excel file not found: {file_path}")

        try:
            # Read Excel file
            df = self._read_excel_file(file_path)

            # Process rows
            processed_orders = []
            total_rows = 0
            successful_imports = 0
            skipped_rows = 0

            for row_num, row_data in self._iterate_data_rows(df):
                total_rows += 1

                try:
                    # Skip empty rows if configured
                    if self.config.skip_empty_rows and self._is_empty_row(row_data):
                        skipped_rows += 1
                        continue

                    # Parse and validate row
                    processed_order = self._process_row(row_num, row_data)

                    if processed_order:
                        processed_orders.append(processed_order)
                        successful_imports += 1

                except Exception as e:
                    self._add_error(row_num, "processing_error", str(e), row_data)

                    # Stop if too many errors
                    if len(self.errors) >= self.config.max_errors:
                        logger.warning(
                            f"Stopping import due to too many errors ({len(self.errors)})"
                        )
                        break

            # Create result
            result = ExcelImportResult(
                total_rows_processed=total_rows,
                successful_imports=successful_imports,
                failed_imports=len(self.errors),
                skipped_rows=skipped_rows,
                errors=[error.dict() for error in self.errors],
                warnings=[warning.dict() for warning in self.warnings],
            )

            return result, processed_orders

        except Exception as e:
            logger.error(f"Error parsing Excel file {file_path}: {str(e)}")
            raise

    def _read_excel_file(self, file_path: Path) -> pd.DataFrame:
        """
        Read Excel file into a DataFrame.

        Args:
            file_path: Path to Excel file

        Returns:
            DataFrame with Excel data
        """
        try:
            # Read without header first to get raw data
            df = pd.read_excel(file_path, header=None, engine="openpyxl")

            logger.info(f"Read Excel file with shape: {df.shape}")
            return df

        except Exception as e:
            logger.error(f"Error reading Excel file: {str(e)}")
            raise

    def _iterate_data_rows(
        self, df: pd.DataFrame
    ) -> Iterator[tuple[int, Dict[str, Any]]]:
        """
        Iterate over data rows in the DataFrame.

        Args:
            df: DataFrame to iterate over

        Yields:
            Tuple of (row_number, row_data_dict)
        """
        mapping = self.config.column_mapping
        start_row = mapping.data_start_row

        for idx in range(start_row, len(df)):
            row = df.iloc[idx]

            # Map columns to field names
            row_data = self._map_row_to_fields(row, mapping)

            # Use 1-based row numbering for user-friendly error messages
            yield idx + 1, row_data

    def _map_row_to_fields(
        self, row: pd.Series, mapping: ExcelColumnMapping
    ) -> Dict[str, Any]:
        """
        Map Excel row to field names using column mapping.

        Args:
            row: Pandas Series representing a row
            mapping: Column mapping configuration

        Returns:
            Dictionary with mapped field names
        """

        def get_cell_value(col_ref: Optional[Union[int, str]]) -> Any:
            """Get cell value by column reference."""
            if col_ref is None:
                return None

            try:
                if isinstance(col_ref, int):
                    if 0 <= col_ref < len(row):
                        value = row.iloc[col_ref]
                        # Convert pandas NaN to None
                        return None if pd.isna(value) else value
                    return None
                else:
                    # Handle string column names if needed
                    return row.get(col_ref)
            except (IndexError, KeyError):
                return None

        return {
            "serial_number": get_cell_value(mapping.serial_number_col),
            "representative_name": get_cell_value(mapping.representative_name_col),
            "customer_name": get_cell_value(mapping.customer_name_col),
            "customer_phone": get_cell_value(mapping.customer_phone_col),
            "company_code": get_cell_value(mapping.company_code_col),
            "price": get_cell_value(mapping.price_col),
            "address": get_cell_value(mapping.address_col),
            "company_name": get_cell_value(mapping.company_name_col),
            "net_amount": get_cell_value(mapping.net_amount_col),
            "status": get_cell_value(mapping.status_col),
        }

    def _is_empty_row(self, row_data: Dict[str, Any]) -> bool:
        """
        Check if a row is empty or contains only whitespace.

        Args:
            row_data: Row data dictionary

        Returns:
            True if row is empty
        """
        # Check required fields
        required_fields = ["customer_name", "customer_phone"]

        for field in required_fields:
            value = row_data.get(field)
            if value is not None and str(value).strip():
                return False

        return True

    def _process_row(
        self, row_num: int, row_data: Dict[str, Any]
    ) -> Optional[ProcessedOrderData]:
        """
        Process a single row of data and validate it.

        Args:
            row_num: Row number for error reporting
            row_data: Raw row data

        Returns:
            ProcessedOrderData if valid, None if invalid
        """
        try:
            # First validate raw data structure
            excel_row = ExcelRowData(**row_data)

            # Convert to processed order data
            processed_data = {
                "order_code": None,  # Will be auto-generated during import
                "customer_name": self._clean_string(excel_row.customer_name),
                "customer_phone": self._clean_string(excel_row.customer_phone),
                "customer_address": self._clean_string(excel_row.address),
                "total_price": excel_row.price,
                "company_code": self._clean_string(excel_row.company_code),
                "customer_company_name": self._clean_string(excel_row.company_name),
                "representative_name": self._clean_string(
                    excel_row.representative_name
                ),
                "net_amount": excel_row.net_amount,
                "status": self._clean_string(excel_row.status),
                "notes": f"Imported from Excel - Row {row_num}",
            }

            # Remove None values for optional fields
            processed_data = {k: v for k, v in processed_data.items() if v is not None}

            # Validate processed data
            processed_order = ProcessedOrderData(**processed_data)

            return processed_order

        except Exception as e:
            self._add_error(row_num, "validation_error", str(e), row_data)
            return None

    def _clean_string(self, value: Any) -> Optional[str]:
        """
        Clean and normalize string values.

        Args:
            value: Value to clean

        Returns:
            Cleaned string or None
        """
        if value is None or pd.isna(value):
            return None

        # Convert to string and strip whitespace
        cleaned = str(value).strip()

        if not cleaned:
            return None

        # Remove extra whitespace
        if self.config.trim_whitespace:
            cleaned = re.sub(r"\s+", " ", cleaned)

        return cleaned

    def _add_error(
        self,
        row_num: int,
        error_type: str,
        message: str,
        row_data: Dict[str, Any] = None,
    ):
        """Add an error to the error list."""
        error = ExcelImportError(
            row_number=row_num,
            error_type=error_type,
            error_message=message,
            row_data=row_data,
        )
        self.errors.append(error)
        logger.warning(f"Row {row_num} error ({error_type}): {message}")

    def _add_warning(
        self,
        row_num: int,
        warning_type: str,
        message: str,
        row_data: Dict[str, Any] = None,
    ):
        """Add a warning to the warning list."""
        warning = ExcelImportWarning(
            row_number=row_num,
            warning_type=warning_type,
            warning_message=message,
            row_data=row_data,
        )
        self.warnings.append(warning)
        logger.info(f"Row {row_num} warning ({warning_type}): {message}")


def parse_excel_orders(
    file_path: Union[str, Path], config: ExcelImportConfig = None
) -> tuple[ExcelImportResult, List[ProcessedOrderData]]:
    """
    Convenience function to parse Excel orders.

    Args:
        file_path: Path to Excel file
        config: Import configuration

    Returns:
        Tuple of (import_result, processed_orders)
    """
    parser = ExcelParserService(config)
    return parser.parse_excel_file(file_path)
