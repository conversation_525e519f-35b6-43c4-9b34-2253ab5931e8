from django.contrib import admin


from .models import (
    Order,
    OrderDeliveryStatus,
    OrderProof,
    OrderAssignmentHistory,
    OrderHandlingStatusHistory,
)


class OrderAdmin(admin.ModelAdmin):
    list_display = ("id", "customer_name", "created_at", "assigned_to")
    list_filter = ("created_at", "order_handling_status", "assigned_to")
    search_fields = ("customer_name", "customer_phone")


admin.site.register(Order, OrderAdmin)
admin.site.register(OrderDeliveryStatus)
admin.site.register(OrderProof)
admin.site.register(OrderAssignmentHistory)
admin.site.register(OrderHandlingStatusHistory)
