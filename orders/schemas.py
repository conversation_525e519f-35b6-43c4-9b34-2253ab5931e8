from ninja import Schema, Field
from ninja_schema import ModelSchema
from typing import List, Optional
from datetime import datetime
from orders.models import (
    Company,
    CompanyChannel,
    Order,
    OrderAssignmentHistory,
    OrderDeliveryStatus,
    OrderHandlingStatusHistory,
    OrderProof,
    OrderHandlingStatus,
)


# --- Company Schemas ---
class CompanyInSchema(Schema):
    code: Optional[str] = None
    name: str = Field(..., min_length=3)
    address: Optional[str] = None
    phone: str = Field(..., min_length=10)
    color_code: str = Field(..., min_length=7, pattern=r"^#[0-9A-Fa-f]{6}$")


class CompanyEditSchema(Schema):
    code: Optional[str] = None
    name: Optional[str] = Field(None, min_length=3)
    address: Optional[str] = Field(None, min_length=10)
    phone: Optional[str] = Field(None, min_length=10)
    color_code: Optional[str] = Field(None, min_length=7, pattern=r"^#[0-9A-Fa-f]{6}$")


class CompanyOutSchema(ModelSchema):
    class Config:
        model = Company
        depth = 1
        include = "__all__"


# --- CompanyChannel Schemas ---
class CompanyChannelInSchema(Schema):
    company_id: int = Field(
        ..., description="ID of the company this channel belongs to"
    )
    name: str = Field(..., min_length=3, description="Name of the channel")
    notes: Optional[str] = Field(None, description="Additional notes about the channel")
    channel_whatsapp_number: str = Field(
        ..., min_length=10, description="WhatsApp number for the channel"
    )


class CompanyChannelEditSchema(Schema):
    company_id: Optional[int] = Field(
        None, description="ID of the company this channel belongs to"
    )
    name: Optional[str] = Field(None, min_length=3, description="Name of the channel")
    notes: Optional[str] = Field(None, description="Additional notes about the channel")
    channel_whatsapp_number: Optional[str] = Field(
        None, min_length=10, description="WhatsApp number for the channel"
    )


class CompanyChannelOutSchema(ModelSchema):
    class Config:
        model = CompanyChannel
        depth = 2
        include = "__all__"


# --- Office Schemas ---
class OfficeEditSchema(Schema):
    name: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = None


# --- Order Schemas ---
class OrderInSchema(Schema):
    code: Optional[str] = None
    notes: Optional[str] = None
    total_price: Optional[float] = None
    customer_name: Optional[str] = None
    customer_phone: Optional[str] = None
    customer_address: Optional[str] = None
    customer_company: Optional[int] = None  # company id
    delivery_deadline_date: Optional[datetime] = Field(None)
    order_delivery_status: Optional[int] = Field(None)  # delivery status id
    breakable: Optional[bool] = None  # whether the order contains breakable items
    order_handling_status: Optional[str] = None


class OrderExtendedOutSchema(ModelSchema):
    class Config:
        model = Order
        depth = 2
        include = "__all__"


class OrderOutSchema(ModelSchema):
    class Config:
        model = Order
        depth = 2
        include = "__all__"


class OrderAssignmentHistoryOutSchema(ModelSchema):
    class Config:
        model = OrderAssignmentHistory
        depth = 2
        include = "__all__"


class OrderHandlingStatusHistoryOutSchema(ModelSchema):
    class Config:
        model = OrderHandlingStatusHistory
        depth = 2
        include = "__all__"


class OrderProofOutSchema(ModelSchema):
    class Config:
        model = OrderProof
        depth = 2
        include = "__all__"


class OrderOutSchemaWithHistory(Schema):
    order: OrderOutSchema
    assigning_history: List[OrderAssignmentHistoryOutSchema]
    handling_status_history: List[OrderHandlingStatusHistoryOutSchema]
    proofs: List[OrderProofOutSchema]


# --- Order List Query Schema ---
class OrderListQuerySchema(Schema):
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    status: Optional[OrderHandlingStatus] = None
    assigned_to: Optional[int] = None


# --- Assign Order ---
class OrderAssignSchema(Schema):
    employee_id: int
    special_commission_rate: Optional[float] = None


# --- Transfer Order ---
class OrderTransferSchema(Schema):
    to_employee_id: int
    proof_type: str
    proof_url: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None


# --- Proof ---
class OrderProofInSchema(Schema):
    proof_type: str
    latitude: Optional[float] = None
    longitude: Optional[float] = None


class OrderProofOutSchema(ModelSchema):
    class Config:
        model = OrderProof
        depth = 2
        include = "__all__"


# --- Complete Order ---
class OrderCompleteSchema(Schema):
    proof_url: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    delivery_customer_payment: Optional[float] = None
    order_delivery_status: Optional[int] = None


class OrderStatusOutSchema(ModelSchema):
    class Config:
        model = OrderDeliveryStatus
        fields = "__all__"
