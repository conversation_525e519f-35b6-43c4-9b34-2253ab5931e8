# Generated by Django 5.2.1 on 2025-05-15 01:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('offices', '0002_alter_office_table'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('address', models.TextField(blank=True)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offices.office')),
            ],
            options={
                'verbose_name': 'Company',
                'verbose_name_plural': 'Companies',
                'db_table': 'companies',
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=255)),
                ('notes', models.TextField(blank=True)),
                ('customer_name', models.CharField(max_length=255)),
                ('customer_phone', models.TextField(help_text='List of customer phone numbers separated by `-`')),
                ('customer_address', models.TextField(blank=True)),
                ('customer_email', models.EmailField(blank=True, max_length=254)),
                ('delivery_deadline_date', models.DateTimeField(blank=True, null=True)),
                ('order_handling_status', models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('ASSIGNED', 'Assigned'), ('COMPLETED', 'Completed')], default='PENDING', max_length=20)),
                ('assigned_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('customer_company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.company')),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offices.office')),
            ],
            options={
                'verbose_name': 'Order',
                'verbose_name_plural': 'Orders',
                'db_table': 'orders',
            },
        ),
        migrations.CreateModel(
            name='OrderAssignmentHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('assigned_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assigned_by', to=settings.AUTH_USER_MODEL)),
                ('assigned_to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offices.office')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='orders.order')),
            ],
            options={
                'verbose_name': 'Order Assignment History',
                'verbose_name_plural': 'Order Assignment Histories',
                'db_table': 'order_assignment_history',
            },
        ),
        migrations.CreateModel(
            name='OrderDeliveryStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('order_default_handling_status', models.CharField(blank=True, choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('ASSIGNED', 'Assigned'), ('COMPLETED', 'Completed')], max_length=20, null=True)),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offices.office')),
            ],
            options={
                'verbose_name': 'Order Delivery Status',
                'verbose_name_plural': 'Order Delivery Statuses',
                'db_table': 'order_delivery_statuses',
            },
        ),
        migrations.AddField(
            model_name='order',
            name='order_delivery_status',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.orderdeliverystatus'),
        ),
        migrations.CreateModel(
            name='OrderProof',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('proof_type', models.CharField(max_length=255)),
                ('proof_url', models.ImageField(upload_to='uploads/order_proofs/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='orders.order')),
                ('proof_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='proof_by', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='OrderHandlingStatusHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('handling_status', models.CharField(blank=True, choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('ASSIGNED', 'Assigned'), ('COMPLETED', 'Completed')], max_length=20, null=True)),
                ('note', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('changed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='changed_by', to=settings.AUTH_USER_MODEL)),
                ('delivery_status', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.orderdeliverystatus')),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offices.office')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='orders.order')),
                ('proof', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.orderproof')),
            ],
            options={
                'verbose_name': 'Order Handling Status History',
                'verbose_name_plural': 'Order Handling Status Histories',
                'db_table': 'order_handling_status_history',
            },
        ),
    ]
