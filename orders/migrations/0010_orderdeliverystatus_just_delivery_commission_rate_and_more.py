# Generated by Django 5.2.1 on 2025-06-20 05:42

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0009_companychannel'),
    ]

    operations = [
        migrations.AddField(
            model_name='orderdeliverystatus',
            name='just_delivery_commission_rate',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='orderdeliverystatus',
            name='percentage_commission_rate',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Percentage of the order total price, null if not applicable', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)]),
        ),
        migrations.AddField(
            model_name='orderdeliverystatus',
            name='percentage_of_order_total_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Percentage of the order total price, null if not applicable', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)]),
        ),
    ]
