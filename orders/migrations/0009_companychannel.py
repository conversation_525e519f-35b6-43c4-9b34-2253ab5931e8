# Generated by Django 5.2.1 on 2025-06-20 03:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('offices', '0002_alter_office_table'),
        ('orders', '0008_order_special_commission_rate'),
    ]

    operations = [
        migrations.CreateModel(
            name='CompanyChannel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('notes', models.TextField(blank=True)),
                ('channel_whatsapp_number', models.CharField(help_text='WhatsApp number for the channel', max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.company')),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offices.office')),
            ],
            options={
                'verbose_name': 'Company Channel',
                'verbose_name_plural': 'Company Channels',
                'db_table': 'company_channels',
            },
        ),
    ]
