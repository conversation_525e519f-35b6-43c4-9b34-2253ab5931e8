# Generated by Django 5.2.1 on 2025-06-06 12:27

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0007_remove_order_customer_email'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='special_commission_rate',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Special commission rate for the order', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(500)]),
        ),
    ]
