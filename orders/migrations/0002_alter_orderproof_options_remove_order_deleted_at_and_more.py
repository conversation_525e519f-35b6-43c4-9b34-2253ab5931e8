# Generated by Django 5.2.1 on 2025-05-15 21:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='orderproof',
            options={'verbose_name': 'Order Proof', 'verbose_name_plural': 'Order Proofs'},
        ),
        migrations.RemoveField(
            model_name='order',
            name='deleted_at',
        ),
        migrations.RemoveField(
            model_name='orderhandlingstatushistory',
            name='deleted_at',
        ),
        migrations.RemoveField(
            model_name='orderproof',
            name='deleted_at',
        ),
        migrations.AddField(
            model_name='company',
            name='color_code',
            field=models.CharField(blank=True, help_text='Hex color code for the company', max_length=20),
        ),
        migrations.AddField(
            model_name='order',
            name='delivery_customer_payment',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Amount paid by the customer', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='total_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Amount customer should pay', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='orderproof',
            name='latitude',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='orderproof',
            name='longitude',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='order',
            name='order_handling_status',
            field=models.CharField(choices=[('PENDING', 'Pending'), ('ASSIGNED', 'Assigned'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed')], default='PENDING', max_length=20),
        ),
        migrations.AlterField(
            model_name='orderdeliverystatus',
            name='order_default_handling_status',
            field=models.CharField(blank=True, choices=[('PENDING', 'Pending'), ('ASSIGNED', 'Assigned'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed')], max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='orderhandlingstatushistory',
            name='handling_status',
            field=models.CharField(blank=True, choices=[('PENDING', 'Pending'), ('ASSIGNED', 'Assigned'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed')], max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='orderproof',
            name='proof_type',
            field=models.CharField(choices=[('PROOF_OF_DELIVERY', 'Proof of Delivery'), ('PROOF_OF_PAYMENT', 'Proof of Payment'), ('PROOF_OF_RETURN', 'Proof of Return')], max_length=50),
        ),
        migrations.AlterField(
            model_name='orderproof',
            name='proof_url',
            field=models.ImageField(blank=True, null=True, upload_to='uploads/order_proofs/'),
        ),
        migrations.AlterModelTable(
            name='orderproof',
            table='order_proofs',
        ),
    ]
