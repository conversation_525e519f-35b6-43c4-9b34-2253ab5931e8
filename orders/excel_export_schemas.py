"""
Schemas for Excel export functionality.
Defines request/response models for order export operations.
"""

from datetime import date, datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum
from django.db import models


class ExportType(str, Enum):
    """Export type enumeration."""

    SELECTIVE = "selective"
    BULK = "bulk"


class OrderStatus(str, Enum):
    """Order status enumeration for filtering."""

    PENDING = "PENDING"
    ASSIGNED = "ASSIGNED"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"


class DateRangeFilter(BaseModel):
    """Date range filter for exports."""

    start_date: Optional[date] = Field(None, description="Start date for filtering")
    end_date: Optional[date] = Field(None, description="End date for filtering")

    @validator("end_date")
    def end_date_after_start_date(cls, v, values):
        """Validate that end_date is after start_date."""
        if v and values.get("start_date") and v < values["start_date"]:
            raise ValueError("end_date must be after start_date")
        return v


class OrderExportFilters(BaseModel):
    """Filters for bulk order export."""

    # Date filters
    created_date_range: Optional[DateRangeFilter] = Field(
        None, description="Filter by creation date"
    )
    updated_date_range: Optional[DateRangeFilter] = Field(
        None, description="Filter by update date"
    )

    # Status filters
    status: Optional[List[OrderStatus]] = Field(
        None, description="Filter by order status"
    )

    # User/Representative filters
    assigned_to_ids: Optional[List[int]] = Field(
        None, description="Filter by assigned user IDs"
    )

    # Company filters
    company_ids: Optional[List[int]] = Field(None, description="Filter by company IDs")
    company_codes: Optional[List[str]] = Field(
        None, description="Filter by company codes"
    )

    # Customer filters
    customer_name_contains: Optional[str] = Field(
        None, description="Filter by customer name (partial match)"
    )
    customer_phone_contains: Optional[str] = Field(
        None, description="Filter by phone number (partial match)"
    )

    # Order value filters
    min_total_price: Optional[float] = Field(
        None, ge=0, description="Minimum order value"
    )
    max_total_price: Optional[float] = Field(
        None, ge=0, description="Maximum order value"
    )

    # Office filter
    office_ids: Optional[List[int]] = Field(None, description="Filter by office IDs")

    # Additional filters
    has_notes: Optional[bool] = Field(
        None, description="Filter orders with/without notes"
    )

    @validator("max_total_price")
    def max_price_greater_than_min(cls, v, values):
        """Validate that max_total_price is greater than min_total_price."""
        if v and values.get("min_total_price") and v < values["min_total_price"]:
            raise ValueError("max_total_price must be greater than min_total_price")
        return v


class SelectiveExportRequest(BaseModel):
    """Request schema for selective order export."""

    export_type: ExportType = Field(ExportType.SELECTIVE, description="Export type")
    order_ids: List[int] = Field(
        ..., min_items=1, description="List of order IDs to export"
    )
    filename: Optional[str] = Field(None, description="Custom filename for the export")
    export_date: Optional[date] = Field(
        None, description="Date to display in the export"
    )

    @validator("order_ids")
    def validate_order_ids(cls, v):
        """Validate order IDs are unique and positive."""
        if len(set(v)) != len(v):
            raise ValueError("order_ids must be unique")
        if any(id <= 0 for id in v):
            raise ValueError("order_ids must be positive integers")
        return v

    @validator("filename")
    def validate_filename(cls, v):
        """Validate filename format."""
        if v:
            # Remove any path separators for security
            v = v.replace("/", "").replace("\\", "").replace("..", "")
            if not v.endswith(".xlsx"):
                v += ".xlsx"
        return v


class BulkExportRequest(BaseModel):
    """Request schema for bulk order export."""

    export_type: ExportType = Field(ExportType.BULK, description="Export type")
    filters: OrderExportFilters = Field(
        default_factory=OrderExportFilters, description="Export filters"
    )
    filename: Optional[str] = Field(None, description="Custom filename for the export")
    export_date: Optional[date] = Field(
        None, description="Date to display in the export"
    )
    limit: Optional[int] = Field(
        None, ge=1, le=10000, description="Maximum number of orders to export"
    )

    @validator("filename")
    def validate_filename(cls, v):
        """Validate filename format."""
        if v:
            # Remove any path separators for security
            v = v.replace("/", "").replace("\\", "").replace("..", "")
            if not v.endswith(".xlsx"):
                v += ".xlsx"
        return v


class ExportResult(BaseModel):
    """Result schema for export operations."""

    success: bool = Field(..., description="Whether the export was successful")
    message: str = Field(..., description="Result message")
    file_path: Optional[str] = Field(None, description="Path to the generated file")
    filename: Optional[str] = Field(None, description="Name of the generated file")
    total_orders: int = Field(..., description="Total number of orders exported")
    export_date: date = Field(..., description="Date of the export")
    representative_name: str = Field(
        ..., description="Representative name used in export"
    )
    processing_time_seconds: Optional[float] = Field(
        None, description="Time taken to process the export"
    )


class ExportError(BaseModel):
    """Error schema for export operations."""

    success: bool = Field(False, description="Always false for errors")
    error_type: str = Field(..., description="Type of error")
    error_code: str = Field(..., description="Specific error code")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(
        None, description="Additional error details"
    )


class ExportPreview(BaseModel):
    """Preview schema for export operations."""

    total_orders: int = Field(
        ..., description="Total number of orders that would be exported"
    )
    sample_orders: List[Dict[str, Any]] = Field(
        ..., description="Sample of orders to be exported"
    )
    filters_applied: Dict[str, Any] = Field(
        ..., description="Summary of applied filters"
    )
    estimated_file_size_mb: Optional[float] = Field(
        None, description="Estimated file size in MB"
    )


class ExportHistory(BaseModel):
    """Schema for export history tracking."""

    id: int = Field(..., description="Export history ID")
    user_id: int = Field(..., description="User who performed the export")
    export_type: ExportType = Field(..., description="Type of export")
    total_orders: int = Field(..., description="Number of orders exported")
    filename: str = Field(..., description="Generated filename")
    export_date: date = Field(..., description="Date of export")
    created_at: datetime = Field(..., description="When the export was created")
    filters_used: Optional[Dict[str, Any]] = Field(
        None, description="Filters used for the export"
    )
    file_size_bytes: Optional[int] = Field(None, description="Size of generated file")
    processing_time_seconds: Optional[float] = Field(
        None, description="Processing time"
    )


# Validation helpers
def validate_export_request(request_data: Dict[str, Any]) -> BaseModel:
    """
    Validate export request data and return appropriate schema.

    Args:
        request_data: Raw request data

    Returns:
        Validated request schema

    Raises:
        ValueError: If validation fails
    """
    export_type = request_data.get("export_type", ExportType.SELECTIVE)

    if export_type == ExportType.SELECTIVE:
        return SelectiveExportRequest(**request_data)
    elif export_type == ExportType.BULK:
        return BulkExportRequest(**request_data)
    else:
        raise ValueError(f"Invalid export_type: {export_type}")


# Filter application helpers
def apply_filters_to_queryset(queryset, filters: OrderExportFilters):
    """
    Apply filters to an order queryset.

    Args:
        queryset: Django QuerySet to filter
        filters: Export filters to apply

    Returns:
        Filtered queryset
    """
    # Date range filters
    if filters.created_date_range:
        if filters.created_date_range.start_date:
            queryset = queryset.filter(
                created_at__date__gte=filters.created_date_range.start_date
            )
        if filters.created_date_range.end_date:
            queryset = queryset.filter(
                created_at__date__lte=filters.created_date_range.end_date
            )

    if filters.updated_date_range:
        if filters.updated_date_range.start_date:
            queryset = queryset.filter(
                updated_at__date__gte=filters.updated_date_range.start_date
            )
        if filters.updated_date_range.end_date:
            queryset = queryset.filter(
                updated_at__date__lte=filters.updated_date_range.end_date
            )

    # Status filters
    if filters.status:
        queryset = queryset.filter(order_handling_status__in=filters.status)

    # User filters
    if filters.assigned_to_ids:
        queryset = queryset.filter(assigned_to_id__in=filters.assigned_to_ids)

    # Company filters
    if filters.company_ids:
        queryset = queryset.filter(customer_company_id__in=filters.company_ids)

    if filters.company_codes:
        queryset = queryset.filter(customer_company__code__in=filters.company_codes)

    # Customer filters
    if filters.customer_name_contains:
        queryset = queryset.filter(
            customer_name__icontains=filters.customer_name_contains
        )

    if filters.customer_phone_contains:
        queryset = queryset.filter(
            customer_phone__icontains=filters.customer_phone_contains
        )

    # Price filters
    if filters.min_total_price is not None:
        queryset = queryset.filter(total_price__gte=filters.min_total_price)

    if filters.max_total_price is not None:
        queryset = queryset.filter(total_price__lte=filters.max_total_price)

    # Office filters
    if filters.office_ids:
        # Filter by office (orders have direct office relationship)
        queryset = queryset.filter(office_id__in=filters.office_ids)

    # Notes filter
    if filters.has_notes is not None:
        if filters.has_notes:
            queryset = queryset.exclude(notes__isnull=True).exclude(notes="")
        else:
            queryset = queryset.filter(
                models.Q(notes__isnull=True) | models.Q(notes="")
            )

    return queryset
