"""
Pydantic schemas for Excel import functionality.
These schemas handle validation and mapping of Excel data to database fields.
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from decimal import Decimal
import re


class ExcelRowData(BaseModel):
    """Schema for a single row of Excel data before processing."""

    serial_number: Optional[Union[int, str]] = None
    representative_name: Optional[Union[int, str]] = None
    customer_name: Optional[str] = None
    customer_phone: Optional[Union[int, str]] = (
        None  # Allow int for Excel numeric phone numbers
    )
    code: Optional[Union[int, str]] = None  # Allow int for Excel numeric codes
    price: Optional[Union[int, float, str]] = None
    address: Optional[str] = None
    company: Optional[Union[int, str]] = (
        None  # Allow int for Excel numeric company codes
    )
    net_amount: Optional[Union[int, float, str]] = None
    status: Optional[str] = None

    class Config:
        # Allow extra fields in case Excel has additional columns
        extra = "allow"


class ProcessedOrderData(BaseModel):
    """Schema for processed order data ready for database insertion."""

    code: str = Field(..., min_length=1, max_length=255)
    customer_name: str = Field(..., min_length=1, max_length=255)
    customer_phone: str = Field(..., min_length=1)
    customer_address: Optional[str] = Field(None, max_length=1000)
    total_price: Optional[Decimal] = Field(None, ge=0)
    customer_company_name: Optional[str] = Field(None, max_length=255)
    notes: Optional[str] = Field(None, max_length=1000)

    # Additional fields that might be derived
    representative_name: Optional[str] = Field(None, max_length=255)
    net_amount: Optional[Decimal] = Field(None, ge=0)
    status: Optional[str] = Field(None, max_length=100)

    @validator("customer_phone")
    def validate_phone(cls, v):
        """Validate and clean phone number."""
        if not v:
            raise ValueError("Phone number is required")

        # Clean phone number - remove spaces, newlines, and non-digit characters except +
        cleaned = re.sub(r"[^\d+\-]", "", str(v))

        # If multiple phone numbers are separated by newlines or dashes, take the first one
        if "\n" in str(v) or "-" in cleaned:
            phones = re.split(r"[\n\-]", str(v))
            cleaned = re.sub(r"[^\d+]", "", phones[0].strip())

        if len(cleaned) < 10:
            raise ValueError(f"Phone number too short: {cleaned}")

        return cleaned

    @validator("total_price", pre=True)
    def validate_price(cls, v):
        """Validate and convert price to Decimal."""
        if v is None or v == "":
            return None

        try:
            # Handle string numbers
            if isinstance(v, str):
                v = v.strip()
                if not v:
                    return None
                # Remove any currency symbols or commas
                v = re.sub(r"[^\d.]", "", v)

            return Decimal(str(v))
        except (ValueError, TypeError):
            raise ValueError(f"Invalid price format: {v}")

    @validator("net_amount", pre=True)
    def validate_net_amount(cls, v):
        """Validate and convert net amount to Decimal."""
        if v is None or v == "":
            return None

        try:
            if isinstance(v, str):
                v = v.strip()
                if not v:
                    return None
                v = re.sub(r"[^\d.]", "", v)

            return Decimal(str(v))
        except (ValueError, TypeError):
            raise ValueError(f"Invalid net amount format: {v}")

    @validator("code")
    def validate_code(cls, v):
        """Validate order code."""
        if not v or not str(v).strip():
            raise ValueError("Order code is required")
        return str(v).strip()

    @validator("customer_name")
    def validate_customer_name(cls, v):
        """Validate customer name."""
        if not v or not str(v).strip():
            raise ValueError("Customer name is required")
        return str(v).strip()


class ExcelImportResult(BaseModel):
    """Schema for Excel import operation results."""

    total_rows_processed: int = 0
    successful_imports: int = 0
    failed_imports: int = 0
    skipped_rows: int = 0
    errors: List[Dict[str, Any]] = Field(default_factory=list)
    warnings: List[Dict[str, Any]] = Field(default_factory=list)
    imported_order_ids: List[int] = Field(default_factory=list)

    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_rows_processed == 0:
            return 0.0
        return (self.successful_imports / self.total_rows_processed) * 100


class ExcelImportError(BaseModel):
    """Schema for individual import errors."""

    row_number: int
    error_type: str
    error_message: str
    row_data: Optional[Dict[str, Any]] = None
    field_name: Optional[str] = None


class ExcelImportWarning(BaseModel):
    """Schema for import warnings."""

    row_number: int
    warning_type: str
    warning_message: str
    row_data: Optional[Dict[str, Any]] = None


class ExcelImportRequest(BaseModel):
    """Schema for Excel import API request."""

    file_name: str
    office_id: Optional[int] = None
    default_company_id: Optional[int] = None
    skip_duplicates: bool = True
    validate_companies: bool = True
    create_missing_companies: bool = False

    class Config:
        schema_extra = {
            "example": {
                "file_name": "orders_import.xlsx",
                "office_id": 1,
                "default_company_id": None,
                "skip_duplicates": True,
                "validate_companies": True,
                "create_missing_companies": False,
            }
        }


class ExcelColumnMapping(BaseModel):
    """Schema for defining how Excel columns map to database fields."""

    # Column indices (0-based) or names
    serial_number_col: Optional[Union[int, str]] = 0
    representative_name_col: Optional[Union[int, str]] = 1
    customer_name_col: Union[int, str] = 2
    customer_phone_col: Union[int, str] = 3
    code_col: Union[int, str] = 4
    price_col: Optional[Union[int, str]] = 5
    address_col: Optional[Union[int, str]] = 6
    company_col: Optional[Union[int, str]] = 7
    net_amount_col: Optional[Union[int, str]] = 8
    status_col: Optional[Union[int, str]] = 9

    # Row settings
    header_row: int = 5  # 0-based index where actual headers are
    data_start_row: int = 6  # 0-based index where data starts

    class Config:
        schema_extra = {
            "example": {
                "customer_name_col": 2,
                "customer_phone_col": 3,
                "code_col": 4,
                "price_col": 5,
                "address_col": 6,
                "company_col": 7,
                "header_row": 5,
                "data_start_row": 6,
            }
        }


class ExcelImportConfig(BaseModel):
    """Configuration for Excel import process."""

    column_mapping: ExcelColumnMapping = Field(default_factory=ExcelColumnMapping)
    batch_size: int = Field(default=100, ge=1, le=1000)
    max_errors: int = Field(default=50, ge=1)
    skip_empty_rows: bool = True
    trim_whitespace: bool = True

    # Validation settings
    require_customer_name: bool = True
    require_customer_phone: bool = True
    require_code: bool = True
    allow_duplicate_codes: bool = False

    class Config:
        schema_extra = {
            "example": {
                "batch_size": 100,
                "max_errors": 50,
                "skip_empty_rows": True,
                "require_customer_name": True,
                "require_customer_phone": True,
                "require_code": True,
                "allow_duplicate_codes": False,
            }
        }
