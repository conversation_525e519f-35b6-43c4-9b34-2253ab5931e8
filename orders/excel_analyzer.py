"""
Excel file analysis utility for understanding order data structure.
This utility helps analyze Excel files to understand their structure and content.
"""

import pandas as pd
import openpyxl
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


class ExcelAnalyzer:
    """Utility class for analyzing Excel file structure and content."""
    
    def __init__(self, file_path: str):
        """
        Initialize the analyzer with an Excel file path.
        
        Args:
            file_path: Path to the Excel file to analyze
        """
        self.file_path = file_path
        self.workbook = None
        self.sheets_info = {}
        
    def analyze_file(self) -> Dict[str, Any]:
        """
        Analyze the Excel file and return comprehensive information about its structure.
        
        Returns:
            Dictionary containing file analysis results
        """
        try:
            # Load workbook with openpyxl for detailed analysis
            self.workbook = openpyxl.load_workbook(self.file_path, data_only=True)
            
            analysis_result = {
                "file_path": self.file_path,
                "sheet_names": self.workbook.sheetnames,
                "total_sheets": len(self.workbook.sheetnames),
                "sheets_analysis": {}
            }
            
            # Analyze each sheet
            for sheet_name in self.workbook.sheetnames:
                sheet_analysis = self._analyze_sheet(sheet_name)
                analysis_result["sheets_analysis"][sheet_name] = sheet_analysis
                
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error analyzing Excel file {self.file_path}: {str(e)}")
            raise
            
    def _analyze_sheet(self, sheet_name: str) -> Dict[str, Any]:
        """
        Analyze a specific sheet in the workbook.
        
        Args:
            sheet_name: Name of the sheet to analyze
            
        Returns:
            Dictionary containing sheet analysis results
        """
        sheet = self.workbook[sheet_name]
        
        # Get sheet dimensions
        max_row = sheet.max_row
        max_col = sheet.max_column
        
        # Read data using pandas for easier analysis
        df = pd.read_excel(self.file_path, sheet_name=sheet_name, header=None)
        
        # Find potential header row
        header_row_index = self._find_header_row(df)
        
        # If header found, re-read with proper header
        if header_row_index is not None:
            df_with_header = pd.read_excel(
                self.file_path, 
                sheet_name=sheet_name, 
                header=header_row_index
            )
        else:
            df_with_header = df
            
        # Analyze columns
        columns_analysis = self._analyze_columns(df_with_header)
        
        # Sample data (first 10 rows)
        sample_data = self._get_sample_data(df_with_header, 10)
        
        return {
            "dimensions": {
                "max_row": max_row,
                "max_column": max_col,
                "data_rows": len(df_with_header),
                "data_columns": len(df_with_header.columns)
            },
            "header_row_index": header_row_index,
            "columns": list(df_with_header.columns),
            "columns_analysis": columns_analysis,
            "sample_data": sample_data,
            "data_types": df_with_header.dtypes.to_dict(),
            "null_counts": df_with_header.isnull().sum().to_dict(),
            "unique_counts": df_with_header.nunique().to_dict()
        }
        
    def _find_header_row(self, df: pd.DataFrame) -> Optional[int]:
        """
        Try to identify which row contains the headers.
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            Index of the header row, or None if not found
        """
        # Look for rows that might contain headers
        # Headers typically have:
        # 1. Text values (not numbers)
        # 2. Unique values across columns
        # 3. No empty cells
        
        for i in range(min(10, len(df))):  # Check first 10 rows
            row = df.iloc[i]
            
            # Skip completely empty rows
            if row.isnull().all():
                continue
                
            # Check if row has mostly text values
            text_count = sum(1 for val in row if isinstance(val, str) and val.strip())
            total_non_null = row.notna().sum()
            
            if text_count > 0 and text_count >= total_non_null * 0.5:
                return i
                
        return None
        
    def _analyze_columns(self, df: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """
        Analyze each column in the DataFrame.
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            Dictionary with analysis for each column
        """
        columns_analysis = {}
        
        for col in df.columns:
            col_data = df[col].dropna()
            
            analysis = {
                "data_type": str(df[col].dtype),
                "null_count": df[col].isnull().sum(),
                "unique_count": df[col].nunique(),
                "sample_values": col_data.head(5).tolist() if len(col_data) > 0 else [],
                "is_numeric": pd.api.types.is_numeric_dtype(df[col]),
                "is_datetime": pd.api.types.is_datetime64_any_dtype(df[col]),
                "is_text": pd.api.types.is_string_dtype(df[col]) or pd.api.types.is_object_dtype(df[col])
            }
            
            # Try to detect potential field mappings
            col_name_lower = str(col).lower().strip()
            analysis["potential_mapping"] = self._suggest_field_mapping(col_name_lower, analysis)
            
            columns_analysis[str(col)] = analysis
            
        return columns_analysis
        
    def _suggest_field_mapping(self, col_name: str, col_analysis: Dict[str, Any]) -> Optional[str]:
        """
        Suggest which database field this column might map to.
        
        Args:
            col_name: Column name (lowercase)
            col_analysis: Analysis results for the column
            
        Returns:
            Suggested field name or None
        """
        # Define mapping patterns
        mapping_patterns = {
            "code": ["code", "كود", "رقم", "id", "order_id", "order_code"],
            "customer_name": ["name", "customer", "اسم", "عميل", "customer_name", "client"],
            "customer_phone": ["phone", "mobile", "هاتف", "موبايل", "تليفون", "رقم_هاتف"],
            "customer_address": ["address", "عنوان", "location", "مكان"],
            "total_price": ["price", "amount", "total", "سعر", "مبلغ", "إجمالي", "cost"],
            "notes": ["notes", "note", "ملاحظات", "تعليق", "description", "وصف"],
            "delivery_deadline_date": ["date", "deadline", "تاريخ", "موعد", "delivery_date"],
            "customer_company": ["company", "شركة", "مؤسسة", "جهة"]
        }
        
        for field, patterns in mapping_patterns.items():
            for pattern in patterns:
                if pattern in col_name:
                    return field
                    
        return None
        
    def _get_sample_data(self, df: pd.DataFrame, num_rows: int = 10) -> List[Dict[str, Any]]:
        """
        Get sample data from the DataFrame.
        
        Args:
            df: DataFrame to sample from
            num_rows: Number of rows to sample
            
        Returns:
            List of dictionaries representing sample rows
        """
        sample_df = df.head(num_rows)
        return sample_df.to_dict('records')
        
    def print_analysis(self, analysis_result: Dict[str, Any]) -> None:
        """
        Print a formatted analysis report.
        
        Args:
            analysis_result: Analysis results from analyze_file()
        """
        print(f"\n=== Excel File Analysis: {analysis_result['file_path']} ===")
        print(f"Total Sheets: {analysis_result['total_sheets']}")
        print(f"Sheet Names: {', '.join(analysis_result['sheet_names'])}")
        
        for sheet_name, sheet_analysis in analysis_result['sheets_analysis'].items():
            print(f"\n--- Sheet: {sheet_name} ---")
            dims = sheet_analysis['dimensions']
            print(f"Dimensions: {dims['data_rows']} rows × {dims['data_columns']} columns")
            print(f"Header Row Index: {sheet_analysis['header_row_index']}")
            
            print("\nColumns:")
            for col, col_analysis in sheet_analysis['columns_analysis'].items():
                mapping = col_analysis.get('potential_mapping', 'Unknown')
                print(f"  - {col} → {mapping} ({col_analysis['data_type']})")
                if col_analysis['sample_values']:
                    print(f"    Sample: {col_analysis['sample_values'][:3]}")
                    
            print(f"\nSample Data (first 3 rows):")
            for i, row in enumerate(sheet_analysis['sample_data'][:3]):
                print(f"  Row {i+1}: {dict(list(row.items())[:3])}...")


def analyze_excel_file(file_path: str) -> Dict[str, Any]:
    """
    Convenience function to analyze an Excel file.
    
    Args:
        file_path: Path to the Excel file
        
    Returns:
        Analysis results dictionary
    """
    analyzer = ExcelAnalyzer(file_path)
    return analyzer.analyze_file()


if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        analyzer = ExcelAnalyzer(file_path)
        try:
            results = analyzer.analyze_file()
            analyzer.print_analysis(results)
        except Exception as e:
            print(f"Error analyzing file: {e}")
    else:
        print("Usage: python excel_analyzer.py <path_to_excel_file>")
