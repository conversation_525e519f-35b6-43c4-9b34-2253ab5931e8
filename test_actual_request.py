#!/usr/bin/env python3
"""
Test with the exact request that might be causing the empty file.
"""

import sys
import os
import json

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_problematic_requests():
    """Test requests that might cause empty files."""
    print("=== Testing Potentially Problematic Requests ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from django.test import RequestFactory
        from orders.api import export_orders_bulk
        from orders.excel_export_schemas import BulkExportRequest
        from accounts.models import User
        
        user = User.objects.first()
        
        # Test requests that might cause issues
        problematic_requests = [
            {
                "name": "Request with empty arrays (common frontend pattern)",
                "payload": {
                    "export_type": "bulk",
                    "filters": {
                        "status": [],
                        "company_ids": [],
                        "assigned_to_ids": [],
                        "company_codes": [],
                        "office_ids": []
                    },
                    "filename": "hello.xlsx",
                    "limit": 1000
                }
            },
            {
                "name": "Request with null values",
                "payload": {
                    "export_type": "bulk",
                    "filters": {
                        "status": None,
                        "company_ids": None,
                        "assigned_to_ids": None
                    },
                    "filename": "hello.xlsx"
                }
            },
            {
                "name": "Request with very high limit",
                "payload": {
                    "export_type": "bulk",
                    "filters": {},
                    "limit": 50000
                }
            },
            {
                "name": "Request with date filters (future dates)",
                "payload": {
                    "export_type": "bulk",
                    "filters": {
                        "created_date_range": {
                            "start_date": "2025-12-01",
                            "end_date": "2025-12-31"
                        }
                    }
                }
            },
            {
                "name": "Request with non-existent company IDs",
                "payload": {
                    "export_type": "bulk",
                    "filters": {
                        "company_ids": [99999, 88888, 77777]
                    }
                }
            }
        ]
        
        factory = RequestFactory()
        
        for test_case in problematic_requests:
            print(f"\n🔍 Testing: {test_case['name']}")
            
            try:
                # Create request
                request = factory.post(
                    '/orders/export-excel/bulk',
                    data=json.dumps(test_case['payload']),
                    content_type='application/json'
                )
                request.auth = user
                
                # Create payload object
                payload = BulkExportRequest(**test_case['payload'])
                
                # Call the API function
                response = export_orders_bulk(request, payload)
                
                print(f"   ✅ API call successful")
                print(f"   Content-Length: {response.get('Content-Length', 'Not set')}")
                
                # Check file content
                try:
                    content_start = response.file_to_stream.read(100)
                    response.file_to_stream.seek(0)
                    
                    if content_start:
                        print(f"   ✅ File has content: {len(content_start)} bytes")
                    else:
                        print(f"   ❌ File is empty!")
                except Exception as e:
                    print(f"   ⚠️  Could not read content: {e}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                # Check if it's a "no orders found" error
                if "No orders found" in str(e):
                    print(f"   📝 This request would result in no orders - expected behavior")
                else:
                    import traceback
                    traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing problematic requests: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_test_file():
    """Create a test file to verify the exact request that caused the empty file."""
    print("\n=== Creating Test File with Exact API Logic ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        from orders.excel_export_service import export_orders_to_excel, generate_representative_name
        from orders.excel_export_schemas import apply_filters_to_queryset, OrderExportFilters
        from accounts.models import User
        from datetime import date
        
        user = User.objects.first()
        
        # Test the exact same request that might have caused the empty file
        print("🔍 Creating file with the same logic as 'hello.xlsx':")
        
        # Simulate the request that created "hello.xlsx"
        payload_data = {
            "export_type": "bulk",
            "filters": {},  # Empty filters
            "filename": "hello_recreated.xlsx",
            "limit": 1000
        }
        
        # Follow exact API logic
        orders = Order.objects.filter(office=user.office).select_related(
            "customer_company", "assigned_to", "order_delivery_status"
        )
        
        print(f"   Base orders: {orders.count()}")
        
        # Apply filters (empty)
        filters = OrderExportFilters()
        orders = apply_filters_to_queryset(orders, filters)
        
        print(f"   After filters: {orders.count()}")
        
        # Apply limit
        if payload_data.get('limit'):
            orders = orders[:payload_data['limit']]
        
        print(f"   After limit: {orders.count()}")
        
        if not orders.exists():
            print("   ❌ No orders exist - this would cause empty file!")
            return False
        
        # Generate representative name
        representative_name = generate_representative_name(user)
        
        # Export to Excel
        file_path = export_orders_to_excel(
            orders=orders,
            representative_name=representative_name,
            export_date=date.today(),
            filename=payload_data.get('filename', 'test.xlsx')
        )
        
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"   ✅ File created: {file_path}")
            print(f"   File size: {file_size:,} bytes")
            
            # Verify content
            import openpyxl
            wb = openpyxl.load_workbook(file_path)
            ws = wb.active
            
            print(f"   Sheet: {ws.title}")
            print(f"   Dimensions: {ws.max_row} rows x {ws.max_column} columns")
            
            # Check for actual data
            data_rows = 0
            for row in range(7, min(12, ws.max_row + 1)):
                row_data = []
                has_data = False
                for col in range(1, 9):
                    cell = ws.cell(row=row, column=col)
                    row_data.append(cell.value)
                    if cell.value is not None and str(cell.value).strip():
                        has_data = True
                
                if has_data:
                    data_rows += 1
                    print(f"   Data Row {row}: {row_data}")
                    if data_rows >= 3:  # Show first 3 rows
                        break
            
            print(f"   Total data rows: {data_rows}")
            
            wb.close()
            
            # Copy to a location you can easily access
            import shutil
            dest_path = "hello_recreated_test.xlsx"
            shutil.copy2(file_path, dest_path)
            print(f"   ✅ File copied to: {dest_path}")
            
            return data_rows > 0
        else:
            print(f"   ❌ File not created")
            return False
        
    except Exception as e:
        print(f"❌ Error creating test file: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Testing Actual Request Scenarios\n")
    
    tests = [
        ("Problematic Requests", test_problematic_requests),
        ("Recreate Test File", create_test_file),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'='*60}")
    
    # Summary
    print(f"\n🏁 Test Results Summary:")
    print(f"{'='*60}")
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\n💡 Recommendations:")
    print(f"1. Check the exact request payload being sent from frontend")
    print(f"2. Use the preview API first to verify orders are found")
    print(f"3. Check browser network tab for the actual response size")
    print(f"4. Verify the file is not being corrupted during download")


if __name__ == "__main__":
    main()
