#!/usr/bin/env python3
"""
Test different filter scenarios that might cause empty results.
"""

import sys
import os
from datetime import date, timedelta

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_filter_scenarios():
    """Test various filter combinations that might result in empty QuerySets."""
    print("=== Testing Filter Scenarios ===")
    
    try:
        # Import Django and setup
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        from orders.excel_export_schemas import apply_filters_to_queryset, OrderExportFilters, DateRangeFilter
        from accounts.models import User
        
        user = User.objects.first()
        base_orders = Order.objects.filter(office=user.office)
        
        print(f"👤 User: {user.username} (Office: {user.office.name})")
        print(f"📊 Total orders in office: {base_orders.count()}")
        
        # Test scenarios that might cause empty results
        scenarios = [
            {
                "name": "Empty filters",
                "filters": OrderExportFilters()
            },
            {
                "name": "Future date range",
                "filters": OrderExportFilters(
                    created_date_range=DateRangeFilter(
                        start_date=date.today() + timedelta(days=1),
                        end_date=date.today() + timedelta(days=30)
                    )
                )
            },
            {
                "name": "Very old date range",
                "filters": OrderExportFilters(
                    created_date_range=DateRangeFilter(
                        start_date=date(2020, 1, 1),
                        end_date=date(2020, 12, 31)
                    )
                )
            },
            {
                "name": "Non-existent status",
                "filters": OrderExportFilters(status=['NON_EXISTENT'])
            },
            {
                "name": "Non-existent company IDs",
                "filters": OrderExportFilters(company_ids=[99999, 88888])
            },
            {
                "name": "Non-existent user IDs",
                "filters": OrderExportFilters(assigned_to_ids=[99999, 88888])
            },
            {
                "name": "Valid status filter",
                "filters": OrderExportFilters(status=['PENDING', 'COMPLETED'])
            },
            {
                "name": "Recent date range",
                "filters": OrderExportFilters(
                    created_date_range=DateRangeFilter(
                        start_date=date.today() - timedelta(days=30),
                        end_date=date.today()
                    )
                )
            },
            {
                "name": "High price filter",
                "filters": OrderExportFilters(min_total_price=10000.0)
            },
            {
                "name": "Low price filter",
                "filters": OrderExportFilters(max_total_price=0.01)
            }
        ]
        
        print(f"\n🔍 Testing {len(scenarios)} filter scenarios:")
        
        empty_scenarios = []
        
        for scenario in scenarios:
            try:
                filtered_orders = apply_filters_to_queryset(base_orders, scenario["filters"])
                count = filtered_orders.count()
                
                status = "✅" if count > 0 else "❌"
                print(f"   {status} {scenario['name']}: {count} orders")
                
                if count == 0:
                    empty_scenarios.append(scenario["name"])
                    
            except Exception as e:
                print(f"   ❌ {scenario['name']}: ERROR - {e}")
                empty_scenarios.append(scenario["name"])
        
        print(f"\n📋 Summary:")
        print(f"   Scenarios with results: {len(scenarios) - len(empty_scenarios)}")
        print(f"   Scenarios with no results: {len(empty_scenarios)}")
        
        if empty_scenarios:
            print(f"   Empty scenarios: {', '.join(empty_scenarios)}")
        
        return len(empty_scenarios) < len(scenarios)  # Pass if at least some scenarios have results
        
    except Exception as e:
        print(f"❌ Error testing filter scenarios: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_actual_api_request():
    """Test with a typical API request payload."""
    print("\n=== Testing Actual API Request ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        from orders.excel_export_schemas import apply_filters_to_queryset, OrderExportFilters, BulkExportRequest
        from orders.excel_export_service import export_orders_to_excel, generate_representative_name
        from accounts.models import User
        
        user = User.objects.first()
        
        # Simulate a typical bulk export request (like what might come from frontend)
        print("🔍 Simulating typical bulk export request:")
        
        # Test 1: Completely empty request (no filters)
        print("\n   Test 1: Empty request")
        try:
            request_data = {
                "export_type": "bulk",
                "filters": {},
                "limit": 1000
            }
            
            bulk_request = BulkExportRequest(**request_data)
            
            orders = Order.objects.filter(office=user.office).select_related(
                "customer_company", "assigned_to", "order_delivery_status"
            )
            
            orders = apply_filters_to_queryset(orders, bulk_request.filters)
            
            if bulk_request.limit:
                orders = orders[:bulk_request.limit]
            
            count = orders.count()
            print(f"      Result: {count} orders")
            
            if count > 0:
                # Try export
                rep_name = generate_representative_name(user)
                file_path = export_orders_to_excel(
                    orders=orders,
                    representative_name=rep_name,
                    export_date=date.today(),
                    filename="test_empty_request.xlsx"
                )
                
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"      Export: ✅ {file_size:,} bytes")
                    
                    # Quick check for data
                    import openpyxl
                    wb = openpyxl.load_workbook(file_path)
                    ws = wb.active
                    
                    # Check if row 7 has data
                    row_7_data = [ws.cell(row=7, column=col).value for col in range(1, 9)]
                    has_data = any(cell is not None and str(cell).strip() for cell in row_7_data)
                    
                    print(f"      Data check: {'✅ Has data' if has_data else '❌ No data'}")
                    if has_data:
                        print(f"      Row 7: {row_7_data}")
                    
                    wb.close()
                else:
                    print(f"      Export: ❌ File not created")
            else:
                print(f"      Export: ❌ No orders to export")
                
        except Exception as e:
            print(f"      Error: {e}")
        
        # Test 2: Request with some filters
        print("\n   Test 2: Request with date filter")
        try:
            request_data = {
                "export_type": "bulk",
                "filters": {
                    "created_date_range": {
                        "start_date": "2025-06-01",
                        "end_date": "2025-06-30"
                    }
                },
                "limit": 100
            }
            
            bulk_request = BulkExportRequest(**request_data)
            
            orders = Order.objects.filter(office=user.office).select_related(
                "customer_company", "assigned_to", "order_delivery_status"
            )
            
            orders = apply_filters_to_queryset(orders, bulk_request.filters)
            
            if bulk_request.limit:
                orders = orders[:bulk_request.limit]
            
            count = orders.count()
            print(f"      Result: {count} orders")
            
        except Exception as e:
            print(f"      Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing actual API request: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_order_data():
    """Check the actual order data to understand what's available."""
    print("\n=== Checking Order Data ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        from accounts.models import User
        
        user = User.objects.first()
        orders = Order.objects.filter(office=user.office)
        
        print(f"📊 Order Statistics:")
        print(f"   Total orders: {orders.count()}")
        
        # Check date ranges
        if orders.exists():
            earliest = orders.order_by('created_at').first()
            latest = orders.order_by('-created_at').first()
            
            print(f"   Date range: {earliest.created_at.date()} to {latest.created_at.date()}")
            
            # Check status distribution
            from django.db.models import Count
            status_counts = orders.values('order_handling_status').annotate(count=Count('id'))
            
            print(f"   Status distribution:")
            for status in status_counts:
                print(f"     {status['order_handling_status']}: {status['count']}")
            
            # Check company distribution
            company_counts = orders.values('customer_company__name').annotate(count=Count('id'))[:5]
            
            print(f"   Top companies:")
            for company in company_counts:
                name = company['customer_company__name'] or 'No Company'
                print(f"     {name}: {company['count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking order data: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Filter Scenario Tests\n")
    
    tests = [
        ("Order Data Check", check_order_data),
        ("Filter Scenarios", test_filter_scenarios),
        ("Actual API Request", test_actual_api_request),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'='*60}")
    
    # Summary
    print(f"\n🏁 Test Results Summary:")
    print(f"{'='*60}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")


if __name__ == "__main__":
    main()
