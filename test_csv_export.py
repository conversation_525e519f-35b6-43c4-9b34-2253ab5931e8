#!/usr/bin/env python3
"""
Test script for CSV export functionality.
"""

import sys
import os
from datetime import date

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_csv_export_service():
    """Test the CSV export service directly."""
    print("=== Testing CSV Export Service ===")
    
    try:
        # Import Django and setup
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        from orders.csv_export_service import export_orders_to_csv, export_orders_to_csv_response, export_orders_to_csv_arabic
        from accounts.models import User
        
        # Get some test orders
        orders = Order.objects.all()[:5]  # Get first 5 orders
        
        if not orders.exists():
            print("❌ No orders found in database for testing")
            return False
        
        print(f"📊 Found {orders.count()} orders for testing")
        
        # Test 1: Basic CSV export to file
        print("\n🔍 Test 1: Basic CSV export to file")
        try:
            file_path = export_orders_to_csv(orders, "test_export.csv")
            
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ File created: {file_path}")
                print(f"   Size: {file_size:,} bytes")
                
                # Read and display first few lines
                with open(file_path, 'r', encoding='utf-8-sig') as f:
                    lines = f.readlines()[:3]  # First 3 lines
                    print(f"   Lines: {len(lines)}")
                    for i, line in enumerate(lines):
                        print(f"   Line {i+1}: {line.strip()[:100]}...")
                
                # Clean up
                os.unlink(file_path)
            else:
                print(f"❌ File not created")
                return False
                
        except Exception as e:
            print(f"❌ Error in basic export: {e}")
            return False
        
        # Test 2: HTTP Response export (English headers)
        print("\n🔍 Test 2: HTTP Response export (English headers)")
        try:
            response = export_orders_to_csv_response(orders, "test_response.csv")
            
            print(f"✅ Response created")
            print(f"   Content-Type: {response.get('Content-Type')}")
            print(f"   Content-Disposition: {response.get('Content-Disposition')}")
            
            # Get content
            content = response.content.decode('utf-8-sig')
            lines = content.split('\n')[:3]  # First 3 lines
            
            print(f"   Content lines: {len(lines)}")
            for i, line in enumerate(lines):
                if line.strip():
                    print(f"   Line {i+1}: {line.strip()[:100]}...")
                    
        except Exception as e:
            print(f"❌ Error in response export: {e}")
            return False
        
        # Test 3: Arabic headers export
        print("\n🔍 Test 3: Arabic headers export")
        try:
            response = export_orders_to_csv_arabic(orders, "test_arabic.csv")
            
            print(f"✅ Arabic response created")
            print(f"   Content-Type: {response.get('Content-Type')}")
            print(f"   Content-Disposition: {response.get('Content-Disposition')}")
            
            # Get content
            content = response.content.decode('utf-8-sig')
            lines = content.split('\n')[:3]  # First 3 lines
            
            print(f"   Content lines: {len(lines)}")
            for i, line in enumerate(lines):
                if line.strip():
                    print(f"   Line {i+1}: {line.strip()[:100]}...")
                    
        except Exception as e:
            print(f"❌ Error in Arabic export: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during CSV export test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_csv_api_endpoints():
    """Test the CSV API endpoints."""
    print("\n=== Testing CSV API Endpoints ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from django.test import RequestFactory
        from orders.api import export_orders_csv_bulk, export_orders_csv_selective, export_orders_csv_bulk_arabic
        from orders.excel_export_schemas import BulkExportRequest, SelectiveExportRequest
        from accounts.models import User
        import json
        
        user = User.objects.first()
        if not user:
            print("❌ No users found")
            return False
        
        print(f"👤 Using user: {user.username}")
        
        factory = RequestFactory()
        
        # Test 1: Selective CSV export
        print("\n🔍 Test 1: Selective CSV export")
        try:
            payload_data = {
                "export_type": "selective",
                "order_ids": [1, 2, 3],
                "filename": "test_selective.csv"
            }
            
            request = factory.post(
                '/orders/export-csv/selective',
                data=json.dumps(payload_data),
                content_type='application/json'
            )
            request.auth = user
            
            payload = SelectiveExportRequest(**payload_data)
            response = export_orders_csv_selective(request, payload)
            
            print(f"✅ Selective CSV export successful")
            print(f"   Content-Type: {response.get('Content-Type')}")
            print(f"   Content-Disposition: {response.get('Content-Disposition')}")
            
            # Check content
            content = response.content.decode('utf-8-sig')
            lines = content.split('\n')
            print(f"   Total lines: {len([l for l in lines if l.strip()])}")
            
        except Exception as e:
            print(f"❌ Error in selective CSV: {e}")
        
        # Test 2: Bulk CSV export
        print("\n🔍 Test 2: Bulk CSV export")
        try:
            payload_data = {
                "export_type": "bulk",
                "filters": {},
                "filename": "test_bulk.csv",
                "limit": 10
            }
            
            request = factory.post(
                '/orders/export-csv/bulk',
                data=json.dumps(payload_data),
                content_type='application/json'
            )
            request.auth = user
            
            payload = BulkExportRequest(**payload_data)
            response = export_orders_csv_bulk(request, payload)
            
            print(f"✅ Bulk CSV export successful")
            print(f"   Content-Type: {response.get('Content-Type')}")
            print(f"   Content-Disposition: {response.get('Content-Disposition')}")
            
            # Check content
            content = response.content.decode('utf-8-sig')
            lines = content.split('\n')
            print(f"   Total lines: {len([l for l in lines if l.strip()])}")
            
        except Exception as e:
            print(f"❌ Error in bulk CSV: {e}")
        
        # Test 3: Arabic CSV export
        print("\n🔍 Test 3: Arabic CSV export")
        try:
            payload_data = {
                "export_type": "bulk",
                "filters": {},
                "filename": "test_arabic.csv",
                "limit": 5
            }
            
            request = factory.post(
                '/orders/export-csv/bulk-arabic',
                data=json.dumps(payload_data),
                content_type='application/json'
            )
            request.auth = user
            
            payload = BulkExportRequest(**payload_data)
            response = export_orders_csv_bulk_arabic(request, payload)
            
            print(f"✅ Arabic CSV export successful")
            print(f"   Content-Type: {response.get('Content-Type')}")
            print(f"   Content-Disposition: {response.get('Content-Disposition')}")
            
            # Check content
            content = response.content.decode('utf-8-sig')
            lines = content.split('\n')
            print(f"   Total lines: {len([l for l in lines if l.strip()])}")
            
            # Show header line (Arabic)
            if lines:
                print(f"   Header: {lines[0][:100]}...")
            
        except Exception as e:
            print(f"❌ Error in Arabic CSV: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing CSV API endpoints: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_csv_vs_excel_comparison():
    """Compare CSV vs Excel export."""
    print("\n=== CSV vs Excel Comparison ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        from orders.csv_export_service import export_orders_to_csv
        from orders.excel_export_service import export_orders_to_excel, generate_representative_name
        from accounts.models import User
        
        user = User.objects.first()
        orders = Order.objects.all()[:10]
        
        if not orders.exists():
            print("❌ No orders found")
            return False
        
        print(f"📊 Comparing exports for {orders.count()} orders")
        
        # CSV export
        print("\n🔍 CSV Export:")
        csv_path = export_orders_to_csv(orders, "comparison_test.csv")
        csv_size = os.path.getsize(csv_path)
        print(f"   File: {csv_path}")
        print(f"   Size: {csv_size:,} bytes")
        
        # Excel export
        print("\n🔍 Excel Export:")
        rep_name = generate_representative_name(user)
        excel_path = export_orders_to_excel(orders, rep_name, date.today(), "comparison_test.xlsx")
        excel_size = os.path.getsize(excel_path)
        print(f"   File: {excel_path}")
        print(f"   Size: {excel_size:,} bytes")
        
        # Comparison
        print(f"\n📋 Comparison:")
        print(f"   CSV size: {csv_size:,} bytes")
        print(f"   Excel size: {excel_size:,} bytes")
        print(f"   Size ratio: {excel_size/csv_size:.1f}x (Excel vs CSV)")
        
        # Clean up
        os.unlink(csv_path)
        # Excel file is in temp directory, will be cleaned up automatically
        
        return True
        
    except Exception as e:
        print(f"❌ Error in comparison test: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all CSV export tests."""
    print("🚀 Starting CSV Export Tests\n")
    
    tests = [
        ("CSV Export Service", test_csv_export_service),
        ("CSV API Endpoints", test_csv_api_endpoints),
        ("CSV vs Excel Comparison", test_csv_vs_excel_comparison),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'='*60}")
    
    # Summary
    print(f"\n🏁 Test Results Summary:")
    print(f"{'='*60}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All CSV export tests passed! The feature is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
